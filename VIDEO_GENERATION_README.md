# 视频生成功能说明

## 概述

本项目已成功集成了文生视频和图生视频功能，使用专门的视频模型（PicFlow系列）来生成高质量视频内容。

## 支持的模型

### PicFlow 文生视频模型
- **模型ID**: `picflow-text-to-video-v1`
- **模型名称**: PicFlow Text-to-Video V1
- **功能**: 根据文本提示词生成视频
- **工作流文件**: `texttovideo.json`

### PicFlow 图生视频模型
- **模型ID**: `picflow-image-to-video-v1`
- **模型名称**: PicFlow Image-to-Video V1
- **功能**: 将静态图片转换为动态视频
- **工作流文件**: `imgtovideo.json`

## API 接口

### 1. 文生视频接口

**端点**: `POST /gen/text-to-video`

**请求参数**:
```json
{
  "prompt": "A beautiful sunset over the ocean with waves",
  "negative_prompt": "blurry, low quality, static",
  "model_id": "picflow-text-to-video-v1",
  "seed": 12345,
  "width": 832,
  "height": 480,
  "duration": 3,
  "steps": 10,
  "cfg": 1.0,
  "sampler": "euler",
  "scheduler": "beta",
  "callback_url": "https://your-callback-url.com/webhook",
  "mark_id": "t2v_001"
}
```

**参数说明**:
- `prompt` (必填): 正向提示词，描述想要生成的视频内容
- `negative_prompt` (可选): 反向提示词，描述不想要的内容
- `model_id` (必填): 视频模型ID
- `seed` (必填): 随机种子
- `width` (可选): 视频宽度，默认832，范围256-1920
- `height` (可选): 视频高度，默认480，范围256-1920
- `duration` (可选): 视频时长（秒），默认3，范围1-10
- `steps` (可选): 采样步数，默认10
- `cfg` (可选): CFG引导强度，默认1.0
- `sampler` (可选): 采样器，默认euler
- `scheduler` (可选): 调度器，默认beta

### 2. 图生视频接口

**端点**: `POST /gen/image-to-video`

**请求参数**:
```json
{
  "img_url": "https://example.com/input-image.jpg",
  "prompt": "A woman dancing gracefully in a garden",
  "negative_prompt": "static, blurry, distorted",
  "model_id": "picflow-image-to-video-v1",
  "seed": 54321,
  "frames": 49,
  "steps": 10,
  "cfg": 1.0,
  "sampler": "uni_pc",
  "scheduler": "simple",
  "callback_url": "https://your-callback-url.com/webhook",
  "mark_id": "i2v_001"
}
```

**参数说明**:
- `img_url` (必填): 输入图片的URL地址
- `prompt` (必填): 正向提示词，描述想要的视频动作或效果
- `negative_prompt` (可选): 反向提示词
- `model_id` (必填): 视频模型ID
- `seed` (必填): 随机种子
- `frames` (可选): 视频帧数，默认49，范围16-120
- `steps` (可选): 采样步数，默认10
- `cfg` (可选): CFG引导强度，默认1.0
- `sampler` (可选): 采样器，默认uni_pc
- `scheduler` (可选): 调度器，默认simple

## cURL 示例

### 文生视频
```bash
curl -X POST "http://localhost:8000/gen/text-to-video" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "prompt": "A beautiful sunset over the ocean",
    "negative_prompt": "blurry, low quality",
    "model_id": "picflow-text-to-video-v1",
    "seed": 12345,
    "width": 832,
    "height": 480,
    "duration": 3
  }'
```

### 图生视频
```bash
curl -X POST "http://localhost:8000/gen/image-to-video" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "img_url": "https://example.com/input-image.jpg",
    "prompt": "A woman dancing gracefully",
    "model_id": "picflow-image-to-video-v1",
    "seed": 54321,
    "frames": 49
  }'
```

## 技术架构

### 模型配置结构
视频模型配置独立于文生图模型，在 `comfy/defines.py` 的 `model_info_dict` 中定义：

```python
'picflow-text-to-video-v1': {
    'obj': TextToVideoWorkflow,
    'type': 'text_to_video',
    'model': 'Wan2.1_T2V_14B_FusionX-Q4_K_S.gguf',
    'model_display': 'PicFlow Text-to-Video V1',
    'desc': 'PicFlow文生视频模型',
    'workflow_file': {'gen': 'texttovideo.json'},
    'default_params': {
        'width': 832,
        'height': 480,
        'duration': 3,
        'steps': 10,
        'cfg': 1.0,
        'sampler': 'euler',
        'scheduler': 'beta'
    }
}
```

### 工作流类
- `TextToVideoWorkflow`: 处理文生视频的工作流
- `ImageToVideoWorkflow`: 处理图生视频的工作流

### 参数验证
使用 Pydantic 模型进行严格的参数验证：
- `TextToVideoItem`: 文生视频参数模型
- `ImageToVideoItem`: 图生视频参数模型

## 扩展新模型

要添加新的视频模型，只需在 `model_info_dict` 中添加新的配置项：

```python
'new-video-model-id': {
    'obj': TextToVideoWorkflow,  # 或 ImageToVideoWorkflow
    'type': 'text_to_video',     # 或 'image_to_video'
    'model': 'new_model_file.gguf',
    'model_display': 'New Video Model',
    'desc': '新的视频模型描述',
    'workflow_file': {'gen': 'new_workflow.json'},
    'default_params': {
        # 默认参数配置
    }
}
```

## 注意事项

1. **API密钥**: 请确保使用有效的API密钥
2. **图片URL**: 图生视频需要提供可访问的图片URL
3. **参数范围**: 请遵守参数的取值范围限制
4. **回调URL**: 建议使用回调URL获取生成结果，因为视频生成通常需要较长时间
5. **模型分离**: 视频模型与文生图模型完全分离，使用不同的配置和工作流

## 错误处理

- 如果提供了无效的 `model_id`，API会返回错误信息
- 参数验证失败时会返回详细的错误描述
- 建议在客户端进行参数预验证以提高用户体验
