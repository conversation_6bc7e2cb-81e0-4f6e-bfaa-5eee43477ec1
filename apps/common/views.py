import copy

from fastapi import APIRouter, Depends
from starlette.requests import Request

from core.Auth import check_auth

from comfy.defines import model_info_dict
from core.Response import resp_200
from utils.time_utils import current_timer

router = APIRouter()


@router.get("/config", summary="生图配置", description="一些支持模型的配置")
async def config(
        req: Request,
        api_key: str = Depends(check_auth),
):
    data = {
        'modelMessage': []
    }
    model_info_dict_bak = copy.deepcopy(model_info_dict)
    for model_id, info in model_info_dict_bak.items():
        if not info.get('status'):
            continue
        default_config = info.get('default_config', {})
        order = info.get('order', 1000)
        desc = info.get('desc', "")
        img2img_workflow_info = info.get('img2img_workflow_info', {})

        support_style_list = []
        # 图生图参数
        for style, s_info in img2img_workflow_info.items():
            #如果 style不为 "openposeControl","cannyControl","depthControl"，则添加到支持的样式列表中
            if style in ['openposeControl', 'cannyControl', 'depthControl']:
                continue
            support_style_list.append({
                'label': s_info.get('label', ''),
                'value': style,
                'avatar': s_info.get('avatar', ''),
                'sort': s_info.get('sort', ''),
                'weight': s_info.get('default_weight', 0.5),  # 默认权重
            })
        item = {
            'modelId': model_id,
            'modelOrder': order,
            'modelDesc': desc,
            'modelDisplay': info.get('model_display', ''),
            'modelAvatar': default_config.pop('modelAvatar', ""),
            'modelType': default_config.pop('modelType', "SD1.5"),
            'defaultHdFixDenoise': default_config.pop('defaultHdFixDenoise', 0.38),  # 重绘幅度
            'defaultConfig': default_config,
            "supportStyleList": support_style_list
        }
        data['modelMessage'].append(item)
        data['modelMessage'].sort(key=lambda x: x['modelOrder'])
    return data


@router.get("/health", summary="健康巡检接口", description="健康巡检接口")
async def callback(
        req: Request,
):
    data = {"status": True, "time": current_timer()}
    return resp_200(data=data)
