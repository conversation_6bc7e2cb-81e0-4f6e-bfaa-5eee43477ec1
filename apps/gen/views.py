from fastapi import APIRouter, Depends
from starlette.requests import Request

from core.Auth import check_auth
from core.Log import error_logger
from core.Response import resp_200, resp_450
from utils.task_utils import get_workflow_obj
from comfy.defines import model_info_dict
from schemas.gen import GenImageItem, RembgItem, UpscaleItem, HiresFixItem, LocalRedrawItem, EnlargeImageItem, \
    LineRecolorItem, ImgVaryItem, Image2TextItem, \
    ImageControlPreProcessItem, TextToVideoItem, ImageToVideoItem

from utils.time_utils import current_timer

router = APIRouter()


async def gen_task(
        *,
        action: str,
        task_type: str,
        params: dict,
        origin_params: dict,
        api_key: str = "",
        resource_query: dict = None
):
    """
    :param action: 生图动作 用作记录日志
    :param task_type:  任务类型
    :param params:  参数
    :param origin_params: 原始参数
    :param api_key:  apikey
    :param resource_query: 查询comfyui的其他参数
    :return:
    """
    callback_url = origin_params.get('callback_url', "")  # 回调地址
    address = origin_params.get('address', "")  # 指定算力节点地址

    info = {
        "create_time": current_timer(),
        "update_time": current_timer(),
        'task_type': task_type,
        'params': params,
        'origin_params': origin_params,
        'comfy_server': address,
        'callback_url': callback_url,
        'api_key': api_key,
    }
    try:
        obj = get_workflow_obj(info)
        if not obj:
            error_logger.info(
                f"{action} failed, please check your parameters, params {params} origin_params {origin_params}")
            return resp_450(message=f'{action} failed, please check your parameters')
        result = obj.run()
        if not result:
            error_logger.info(
                f"{action} prompt failed, please check your parameters, params {params},origin_params {origin_params}")
            return resp_450(message=f"{action} run failed, please try again later")
        queue_result = obj.queue_result
        prompt_id = queue_result.get('prompt_id')
        number = queue_result.get("number")  # 任务队列号
        if not all([
            prompt_id,
            number is not None
        ]):
            error_logger.info(f"{action} task failed, {queue_result.get('error', '')}, origin_params {origin_params}")
            return resp_450(message=f"{action} task failed, {queue_result.get('error', '')}")

    except Exception as e:
        error_logger.info(f"{action} task failed, server error: {e}, origin_params {origin_params}")
        return resp_450(message=f"{action} task failed, server error: {e}")

    mark_id = origin_params.get('mark_id', "")
    data = {"task_id": "", "prompt_id": prompt_id, "number": number, "mark_id": mark_id}
    return resp_200(data=data)


@router.post("/prompt", summary="生图接口", description="生图接口")
async def gen(
        req: Request,
        data: GenImageItem,
        api_key: str = Depends(check_auth)
):
    origin_params = dict(data)
    params = dict(data)
    model_id = params['model_id']
    info = model_info_dict.get(model_id, {})
    task_type = info.get('type', "")
    params['task_type'] = task_type
    resource_query = {}
    resp = await gen_task(
        action='image-prompt-gen',
        task_type=task_type,
        params=params,
        origin_params=origin_params,
        api_key=api_key,
        resource_query=resource_query
    )
    return resp


@router.post("/rembg", summary="去背景", description="去背景")
async def rmbg(
        req: Request,
        data: RembgItem,
        api_key: str = Depends(check_auth),
):
    origin_params = dict(data)
    rmbg_type = origin_params.get('type')
    img_url = origin_params.get('img_url')
    params = {
        'type': rmbg_type,
        'img_url': img_url
    }
    resp = await gen_task(
        action="image-rembg",
        task_type=rmbg_type,
        params=params,
        origin_params=origin_params,
        api_key=api_key
    )
    return resp


@router.post("/upscale", summary="超分放大", description="超分放大")
async def upscale(
        req: Request,
        data: UpscaleItem,
        api_key: str = Depends(check_auth),
):
    origin_params = dict(data)
    img_url = origin_params.get('img_url')
    upscale_type = origin_params.get('type')
    scale_by = origin_params.get('scale_by')

    params = {
        'type': upscale_type,
        'img_url': img_url,
        'scale_by': scale_by
    }

    resp = await gen_task(
        action="image-upscale",
        task_type=upscale_type,
        params=params,
        origin_params=origin_params,
        api_key=api_key
    )
    return resp


@router.post("/hires-fix", summary="超分修复", description="超分修复")
async def hires_fix(
        req: Request,
        data: HiresFixItem,
        api_key: str = Depends(check_auth),

):
    data = dict(data)
    img_url = data.get('img_url')
    scale_by = data.get('scale_by')
    callback_url = data.get('callback_url', '')
    upscale_method = data.get('upscale_method', '')
    hd_denoise = data.get('hd_denoise')
    model_id = data['model_id']
    task_type = model_info_dict.get(model_id, {}).get('type', "")
    params = {
        'img_url': img_url,
        "prompt": data['prompt'],
        "model_id": model_id,
        "seed": data['seed'],
        "negative_prompt": data['negative_prompt'],
        "sampler": data['sampler'],
        "scheduler": data['scheduler'],
        "steps": data['steps'],
        "cfg": data['cfg'],
        "denoise": data['denoise'],
        "hd_denoise": hd_denoise,
        "batch_size": 1,
        "height": data['height'],
        "width": data['width'],
        "speed": "normal",
        "callback_url": callback_url,
        "task_type": task_type,
        "scale": scale_by,
        "upscale_method": upscale_method,
        "gen_type": "hires_fix"
    }

    resp = await gen_task(
        action="image-hd-fix",
        task_type=task_type,
        params=params,
        origin_params=data,
        api_key=api_key
    )
    return resp


@router.post(
    "/local_redraw",
    summary="局部重绘",
    description="局部重绘"
)
async def local_redraw(
        req: Request,
        data: LocalRedrawItem,
        api_key: str = Depends(check_auth),
):
    origin_params = dict(data)
    data = dict(data)
    img_url = data.get('img_url')
    mask_img_url = data.get('mask_img_url')
    local_redraw_model_id = data.get('model_id')
    params = {
        'denoise': data.get('denoise'),
        'local_redraw_model_id': local_redraw_model_id,
        'prompt': data.get('prompt'),
        'img_url': img_url,
        'mask_img_url': mask_img_url,
    }
    # 不同模型重绘有不通的json工作流
    task_type = model_info_dict.get(local_redraw_model_id, {}).get("local_redraw_config", {}).get("redraw_type")
    if not task_type:
        task_type = "local_redraw"
    resp = await gen_task(
        action="image-local-redraw",
        task_type=task_type,
        params=params,
        origin_params=origin_params,
        api_key=api_key
    )
    return resp


@router.post(
    "/enlarge_image",
    summary="扩图",
    description="扩图"
)
async def enlarge_image(
        req: Request,
        data: EnlargeImageItem,
        api_key: str = Depends(check_auth),
):
    origin_params = dict(data)
    data = dict(data)
    model_id = data.pop('model_id')
    params = {
        'enlarge_image_model_id': model_id,
    }
    params.update(data)

    # 不同模型扩图有不通的json工作流
    task_type = model_info_dict.get(model_id, {}).get("enlarge_img_config", {}).get("enlarge_type")
    if not task_type:
        task_type = "enlarge_image"
    resp = await gen_task(
        action="image-enlarge-image",
        task_type=task_type,
        params=params,
        origin_params=origin_params,
        api_key=api_key
    )
    return resp


@router.post(
    "/line_recolor",
    summary="上色",
    description="上色"
)
async def line_recolor(
        req: Request,
        data: LineRecolorItem,
        api_key: str = Depends(check_auth),
):
    origin_params = dict(data)
    params = dict(data)
    line_recolor_model_id = params.pop('model_id', None)  # 必须pop掉model_id 否则会认为是文生图
    params.update({
        'line_recolor_model_id': line_recolor_model_id
    })
    resp = await gen_task(
        action="line-recolor",
        task_type="line_recolor",
        params=params,
        origin_params=origin_params,
        api_key=api_key
    )
    return resp


@router.post(
    "/vary",
    summary="图片变化",
    description="图片变化"
)
async def vary(
        req: Request,
        data: ImgVaryItem,
        api_key: str = Depends(check_auth),
):
    origin_params = dict(data)
    params = dict(data)
    vary_model_id = params.pop('model_id', None)  # 必须pop掉model_id 否则会认为是文生图

    if params.get('flag') == 'img_vary_wd':
        task_type = 'img_vary_wd'
        action = "image-vary-wd"
    else:
        params.update({
            'vary_model_id': vary_model_id
        })
        task_type = 'img_vary'
        action = "image-vary"
    resp = await gen_task(
        action=action,
        task_type=task_type,
        params=params,
        origin_params=origin_params,
        api_key=api_key
    )
    return resp


@router.post(
    "/img2text",
    summary="img2text",
    description="img2text"
)
async def img2text(
        req: Request,
        data: Image2TextItem,
        api_key: str = Depends(check_auth),
):
    origin_params = dict(data)
    _type = origin_params.get('type')
    img_url = origin_params.get('img_url')
    params = {
        'type': _type,
        'img_url': img_url
    }
    resp = await gen_task(
        action="img2text",
        task_type='img2text',
        params=params,
        origin_params=origin_params,
        api_key=api_key
    )
    return resp

@router.post("/image_control_pre_process", summary="image_control预处理任务", description="image_control预处理任务")
async def image_control_pre_process(
        req: Request,
        data: ImageControlPreProcessItem,
        api_key: str = Depends(check_auth),
):
    origin_params = dict(data)
    process_type = origin_params.get('type')
    img_url = origin_params.get('img_url')
    params = {
        'type': process_type,
        'img_url': img_url
    }
    resp = await gen_task(
        action="image_control",
        task_type=process_type,
        params=params,
        origin_params=origin_params,
        api_key=api_key
    )
    return resp
