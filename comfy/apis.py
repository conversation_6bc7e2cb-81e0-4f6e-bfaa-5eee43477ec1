import copy
import io
import json
import os
from typing import Union

import requests

from core.Log import error_logger
from utils.consts import *
from conf.settings import DEFAULT_CLIENT_ID
from utils.utils import get_seed


class BaseComfy(object):

    def __init__(self, prompt_id="", client_id="", *, server_address, **kwargs):
        self.prompt_id = prompt_id or ""
        self.prompt = {}  # 退任务的prompt info
        self.queue_result = {}  # 推任务到comf返回的数据 里面有prompt_id number
        self.file_info = {}  # 上传的图片信息 用于去背景超分等功能
        self.server_address = server_address  # comfy地址
        self.client_id = DEFAULT_CLIENT_ID  # client id
        self.model_name = kwargs.get("model_name", "")  # 模型名
        self.extra_params = kwargs.get("extra_params", {})  # 其他参数
        self.workflow_file = kwargs.get("workflow_file", "")  # 对应workflow文件
        self.priority = kwargs.get("priority", None)  # 优先级 用于插队
        self.fast_config = kwargs.get("fast_config", {})  # fast生图的模式配置
        self.timeout = kwargs.get("timeout", 15)  # 默认15秒超时时间

    @property
    def is_fast(self):
        return bool(self.fast_config)

    def queue_prompt(self):
        """
        任务推向队列
        """
        data = {"prompt": self.prompt, "client_id": self.client_id}
        # 插队
        if self.priority:
            data['number'] = self.priority
        res = requests.post(f"http://{self.server_address}/prompt", json=data, timeout=self.timeout)
        result = res.json()
        self.queue_result = result
        return result

    def upload_image(self, image_data: Union[bytes,]):
        buffer = io.BytesIO(image_data)
        files = {'image': buffer}
        response = requests.post(f'http://{self.server_address}/upload/image', files=files, timeout=self.timeout)
        self.file_info = response.json()

    def upload_image_by_url(self, img_url):
        if not img_url:
            self.file_info = {}
            return
        data = {'img_url': img_url}
        response = requests.post(f'http://{self.server_address}/upload/by_url_image', json=data, timeout=self.timeout)
        file_info = response.json()
        self.file_info = file_info
        return file_info

    def get_queue_info(self):
        url = f"http://{self.server_address}/queue"
        res = requests.get(url, timeout=self.timeout)
        data = res.json()
        result = {task[1]: {"num": task[0], "status": status, "prompt_id": task[1], "client_id": task[3]['client_id'],
                            "detail": json.dumps(task)}
                  for status, tasks in data.items() for task in tasks}
        return result

    def get_base_prompt(self, filename: str = "") -> dict:
        filename = filename or self.workflow_file
        dir_name = os.path.dirname(__file__)
        file_path = os.path.join(dir_name, 'workflows', filename)
        with open(file_path, encoding='utf-8') as f:
            prompt = json.load(f)
        accelerated = self.extra_params.get("accelerated", True)  # 是否加速 默认加速
        if not accelerated:
            # 不加速就替换piclumen加速框架和缓存模型的loader为最基础的
            prompt_str = json.dumps(prompt, ensure_ascii=False)
            prompt_str = prompt_str.replace(r'CheckpointLoaderSimpleShared //Inspire',
                                            'CheckpointLoaderSimple').replace(r'PicLumenLoader',
                                                                              'CheckpointLoaderSimple')
            prompt = json.loads(prompt_str)
        return prompt

    def get_prompt(self, *args, **kwargs):
        raise NotImplementedError

    def run(self):
        try:
            img_url = self.extra_params.get('img_url')  # 超分 去背景 支持直接传url
            filepath = self.extra_params.get('filepath')  # 超分 去背景的图片在本地的路径 支持传文件
            if img_url or filepath:
                if img_url:
                    self.upload_image_by_url(img_url)
                elif filepath:
                    with open(filepath, 'rb') as f:
                        image_data = f.read()
                    self.upload_image(image_data)
                file_name = self.file_info['name']
                self.get_prompt(file_name=file_name)
                self.queue_prompt()  # 加任务
            else:
                self.get_prompt()
                self.queue_prompt()
            return True
        except Exception as e:
            error_logger.info(f"gen prompt_id error {e}")
            return False


class Image2ImageWorkflow(BaseComfy):
    def run(self):
        try:
            img_url = self.extra_params.get('img_url')
            filepath = self.extra_params.get('filepath')
            if img_url:
                self.upload_image_by_url(img_url)
            elif filepath:
                with open(filepath, 'rb') as f:
                    image_data = f.read()
                self.upload_image(image_data)
            else:
                return False
            file_name = self.file_info['name']
            self.get_prompt(file_name=file_name)
            self.queue_prompt()  # 加任务
            return True
        except Exception as e:
            error_logger.info(f"i2i tool gen prompt_id error {e}")
            return False


class RmbgWorkflow(Image2ImageWorkflow):
    """
    移除物体背景
    """

    def get_prompt(self, *, file_name):
        prompt = self.get_base_prompt()
        prompt['3']['inputs']['image'] = file_name
        self.prompt = prompt

class OpenposeWorkflow(Image2ImageWorkflow):
    """
    预处理任务pose
    """

    def get_prompt(self, *, file_name):
        prompt = self.get_base_prompt()
        prompt['1']['inputs']['image'] = file_name
        self.prompt = prompt

class CannyProcessWorkflow(Image2ImageWorkflow):
    """
    canny预处理任务
    """

    def get_prompt(self, *, file_name):
        prompt = self.get_base_prompt()
        prompt['1']['inputs']['image'] = file_name
        self.prompt = prompt

class DepthProcessWorkflow(Image2ImageWorkflow):
    """
    depth预处理任务
    """

    def get_prompt(self, *, file_name):
        prompt = self.get_base_prompt()
        prompt['1']['inputs']['image'] = file_name
        self.prompt = prompt


class UpscaleWorkflow(Image2ImageWorkflow):

    def get_prompt(self, *, file_name):
        prompt = self.get_base_prompt()
        scale_by = self.extra_params.get('scale_by')
        prompt['43']['inputs']['image'] = file_name
        prompt['45']['inputs']['model_name'] = self.model_name
        prompt['47']['inputs']['scale_by'] = scale_by / 4
        # prompt['45']['inputs']['image'] = file_name
        # # prompt['45']['inputs']['model_name'] = self.model_name
        # prompt['140']['inputs']['scale_by'] = scale_by / 4
        self.prompt = prompt


class ReRedrawWorkflow(Image2ImageWorkflow):
    """
    局部重绘
    """

    def get_prompt(self, *, file_name, mask_file_name):
        params = self.extra_params
        denoise = params.get('denoise')
        prompt = self.get_base_prompt()
        prompt['71']['inputs']['image'] = mask_file_name
        prompt['11']['inputs']['image'] = file_name
        text = params.get('prompt')  # 正向提示词

        if denoise is not None:
            prompt['3']['inputs']['denoise'] = denoise
        prompt['3']['inputs']['seed'] = get_seed()

        prompt['6']['inputs']['text'] = text
        local_redraw_model_id = params.get('local_redraw_model_id')

        from comfy.defines import model_info_dict

        model_name = model_info_dict.get(local_redraw_model_id, {}).get('model', '')

        local_redraw_config = model_info_dict.get(local_redraw_model_id, {}).get('local_redraw_config', {})
        negative_prompt = local_redraw_config.pop('negative_prompt', None)
        strength = local_redraw_config.pop('strength', None)
        if negative_prompt is not None:
            prompt['7']['inputs']['text'] = negative_prompt

        if strength is not None:
            prompt['15']['inputs']['strength'] = strength

        if local_redraw_config:
            prompt['3']['inputs'].update(local_redraw_config)

        if model_name:
            prompt['4']['inputs']['ckpt_name'] = model_name
        self.prompt = prompt

    def run(self):
        try:
            img_url = self.extra_params.get('img_url')
            self.upload_image_by_url(img_url)
            file_name = self.file_info['name']
            mask_img_url = self.extra_params.get('mask_img_url')
            self.upload_image_by_url(mask_img_url)
            mask_file_name = self.file_info['name']
            self.get_prompt(file_name=file_name, mask_file_name=mask_file_name)
            self.queue_prompt()  # 加任务
            return True
        except Exception as e:
            error_logger.info(f"redraw gen prompt_id error {e}")
            return False


class FluxDevReRedrawWorkflow(Image2ImageWorkflow):
    """
    flux dev局部重绘
    """

    def get_prompt(self, *, file_name, mask_file_name):
        params = self.extra_params
        prompt = self.get_base_prompt()
        text = params.get('prompt')  # 正向提示词
        prompt['17']['inputs']['image'] = file_name
        prompt['62']['inputs']['image'] = mask_file_name
        prompt['3']['inputs']['seed'] = get_seed()
        prompt['23']['inputs']['text'] = text
        self.prompt = prompt

    def run(self):
        try:
            img_url = self.extra_params.get('img_url')
            self.upload_image_by_url(img_url)
            file_name = self.file_info['name']
            mask_img_url = self.extra_params.get('mask_img_url')
            self.upload_image_by_url(mask_img_url)
            mask_file_name = self.file_info['name']
            self.get_prompt(file_name=file_name, mask_file_name=mask_file_name)
            self.queue_prompt()  # 加任务
            return True
        except Exception as e:
            error_logger.info(f"redraw gen prompt_id error {e}")
            return False


class EnlargeImageWorkflow(Image2ImageWorkflow):
    """
    扩图
    """

    def get_prompt(self, *, file_name):
        params = self.extra_params
        prompt = self.get_base_prompt()
        text = params.get('prompt')  # 正向提示词
        # batch_size = params.get('batch_size', 1)  # 目前仅支持一张
        left = params.get('left', 200)
        right = params.get('right', 200)
        top = params.get('top', 0)
        bottom = params.get('bottom', 0)
        denoise = params.get('denoise')
        seed = params.get('seed', get_seed())
        prompt['11']['inputs']['image'] = file_name

        if denoise is not None:
            prompt['3']['inputs']['denoise'] = denoise
        prompt['3']['inputs']['seed'] = seed

        prompt['6']['inputs']['text'] = text
        enlarge_image_model_id = params.get('enlarge_image_model_id')

        from comfy.defines import model_info_dict

        model_name = model_info_dict.get(enlarge_image_model_id, {}).get('model', '')
        if model_name:
            prompt['4']['inputs']['ckpt_name'] = model_name
        enlarge_img_config = model_info_dict.get(enlarge_image_model_id, {}).get('enlarge_img_config', {})
        enlarge_img_config_copy = copy.deepcopy(enlarge_img_config)
        negative_prompt = enlarge_img_config_copy.pop('negative_prompt', "")
        strength = enlarge_img_config_copy.pop('strength', None)

        if strength is not None:
            prompt['15']['inputs']['strength'] = strength

        prompt['3']['inputs'].update(enlarge_img_config_copy)
        prompt['7']['inputs']['text'] = negative_prompt

        prompt['10']['inputs'].update({
            "left": left,
            "right": right,
            "top": top,
            "bottom": bottom,
        })

        self.prompt = prompt


class FluxDevEnlargeImageWorkflow(Image2ImageWorkflow):
    """
    flux DEV 扩图
    """

    def get_prompt(self, *, file_name):
        params = self.extra_params
        prompt = self.get_base_prompt()
        text = params.get('prompt')  # 正向提示词
        left = params.get('left', 200)
        right = params.get('right', 200)
        top = params.get('top', 0)
        bottom = params.get('bottom', 0)

        seed = params.get('seed', get_seed())
        prompt['3']['inputs']['seed'] = seed
        prompt['23']['inputs']['text'] = text
        prompt['45']['inputs']['image'] = file_name
        prompt['44']['inputs'].update({
            "left": left,
            "right": right,
            "top": top,
            "bottom": bottom,
        })

        self.prompt = prompt


class LineRecolorWorkflow(Image2ImageWorkflow):
    """
    线稿上色
    """

    def get_prompt(self, *, file_name):
        params = self.extra_params
        line_recolor_model_id = params.get('line_recolor_model_id')
        prompt = self.get_base_prompt()
        text = params.get('prompt')  # 正向提示词
        batch_size = params.get('batch_size', 1)  # 目前仅支持一张
        seed = params.get('seed', get_seed())
        from comfy.defines import model_info_dict
        model_name = model_info_dict.get(line_recolor_model_id, {}).get('model', '')

        line_recolor_config = model_info_dict.get(line_recolor_model_id, {}).get('line_recolor_config', {})
        negative_prompt = line_recolor_config.pop('negative_prompt', None)

        if negative_prompt is not None:
            prompt['7']['inputs']['text'] = negative_prompt
        if line_recolor_config:
            prompt['3']['inputs'].update(line_recolor_config)

        if model_name:
            prompt['81']['inputs']['ckpt_name'] = model_name
        prompt['3']['inputs']['seed'] = seed
        prompt['76']['inputs']['batch_size'] = batch_size
        prompt['67']['inputs']['image'] = file_name
        prompt['6']['inputs']['text'] = text

        self.prompt = prompt


class ImgVaryWorkflow(Image2ImageWorkflow):
    """
    图片变化
    """

    def get_prompt(self, *, file_name):
        params = self.extra_params
        vary_model_id = params.get('vary_model_id')
        prompt = self.get_base_prompt()
        text = params.get('prompt') or ""  # 正向提示词
        negative_text = params.get('negative_prompt') or ""  # 正向提示词
        batch_size = params.get('batch_size', 1)  # 目前仅支持一张
        seed = params.get('seed', get_seed())
        denoise = params.get('strength')
        from comfy.defines import model_info_dict

        if vary_model_id:
            model_name = model_info_dict.get(vary_model_id, {}).get('model', '')
            if model_name:
                prompt['27']['inputs']['ckpt_name'] = model_name
        if denoise:
            prompt['3']['inputs']['denoise'] = denoise
        default_config = model_info_dict.get(vary_model_id, {}).get('img_vary_config', {})  # vary默认配置
        prompt['3']['inputs'].update(default_config)
        prompt['3']['inputs']['seed'] = seed
        prompt['48']['inputs']['amount'] = batch_size
        prompt['52']['inputs']['image'] = file_name
        prompt['6']['inputs']['text'] = text
        prompt['7']['inputs']['text'] = negative_text

        self.prompt = prompt


class ImgVaryWdWorkflow(Image2ImageWorkflow):
    """
    图片变化-提示词反推
    """

    def get_prompt(self, *, file_name):
        params = self.extra_params
        prompt = self.get_base_prompt()

        batch_size = params.get('batch_size', 1)  # 目前仅支持一张
        seed = params.get('seed', get_seed())
        denoise = params.get('strength')
        if denoise:
            prompt['3']['inputs']['denoise'] = denoise
        prompt['3']['inputs']['seed'] = seed
        prompt['48']['inputs']['amount'] = batch_size
        prompt['52']['inputs']['image'] = file_name

        self.prompt = prompt


class GenImageWorkflow(BaseComfy):

    def get_prompt(self, **kwargs):
        self.kwargs = kwargs
        gen_type = self.extra_params.get('gen_type', 'gen')
        # 真实感
        if gen_type == 'gen_and_hires_fix':
            self.get_gen_and_hires_fix_prompt()  # 生图 + 修复
        elif gen_type == 'hires_fix':
            self.get_hires_fix_prompt()  # 修复
        else:
            self.get_gen_prompt()  # 生图

    def get_gen_and_hires_fix_prompt(self):
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')

        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['3']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        prompt['6']['inputs']['text'] = text  # 正向提示词
        prompt['7']['inputs']['text'] = negative_text  # 反向提示词

        prompt['4']['inputs']['ckpt_name'] = model_name  # 模型名字

        prompt['5']['inputs'].update({
            'batch_size': batch_size,
            'height': height,
            'width': width
        })

        extra_data = kwargs.get('extra_data', {})
        hd_fix_data = extra_data.get('hires_fix', {})
        hd_fix_switch = hd_fix_data.get('switch', False)
        if hd_fix_switch:
            hd_fix_denoise = hd_fix_data.get('denoise', 0.5)
            scale = hd_fix_data.get('scale', 1.5)
            upscale_method = hd_fix_data.get('upscale_method', 'lanczos')
            prompt['16']['inputs']['upscale_method'] = upscale_method
            prompt['16']['inputs']['scale_by'] = scale / 4  # 默认使用的四倍超分

            prompt['20']['inputs'].update({
                'seed': seed,
                'cfg': cfg,
                'sampler_name': sampler_name,
                'scheduler': scheduler,
                'denoise': hd_fix_denoise
            })

        self.prompt = prompt

    def get_gen_prompt(self):
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')

        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE
        height = kwargs.get('height')
        width = kwargs.get('width')
        if self.is_fast:
            sampler_name = self.fast_config.get('sampler_name', sampler_name)
            scheduler = self.fast_config.get('scheduler', scheduler)
            steps = self.fast_config.get('steps', steps)
            cfg = self.fast_config.get('cfg', cfg)
            prompt['10']['inputs']['strength_model'] = 1  # 快速生图lightling的lora系数
            prompt['10']['inputs']['strength_clip'] = 1

        prompt['3']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        prompt['6']['inputs']['text'] = text  # 正向提示词
        prompt['7']['inputs']['text'] = negative_text  # 反向提示词

        prompt['4']['inputs']['ckpt_name'] = model_name  # 模型名字

        prompt['5']['inputs'].update({
            'batch_size': batch_size,
            'height': height,
            'width': width
        })
        self.prompt = prompt

    def get_hires_fix_prompt(self):
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        hd_denoise = kwargs.get('hd_denoise')  # 重绘幅度

        prompt['20']['inputs'].update({
            'seed': seed,
            'steps': 12,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            # 'denoise': denoise
        })

        prompt['6']['inputs']['text'] = text  # 正向提示词
        prompt['7']['inputs']['text'] = negative_text  # 反向提示词

        prompt['4']['inputs']['ckpt_name'] = model_name  # 模型名字
        scale = kwargs.get('scale', 1.5)  # 超分倍数
        upscale_method = kwargs.get('upscale_method', 'lanczos')
        prompt['16']['inputs']['upscale_method'] = upscale_method
        prompt['16']['inputs']['scale_by'] = scale / 4
        filename = self.kwargs.get('file_name')  # 文件上传后在comfyui的文件名
        prompt['24']['inputs']['image'] = filename
        if hd_denoise is not None:
            prompt['20']['inputs']['denoise'] = hd_denoise
        self.prompt = prompt


class Img2ImgGenImageWorkflow(BaseComfy):

    def get_prompt(self, **kwargs):
        self.kwargs = kwargs
        img2img_info = self.extra_params.get('img2img_info', {})
        style = img2img_info.get('style', "")
        img_url = img2img_info.get('img_url', "")
        self.upload_image_by_url(img_url)

        func = getattr(self, f'get_i2i_{style}_prompt', None)
        if not func:
            raise ValueError(f"img2img style {style} not support")
        func()

    def get_i2i_characterRefer_prompt(self):
        """
        角色一致性 i2i_pulid.json
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')

        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['3']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        prompt['4']['inputs']['ckpt_name'] = model_name  # 模型名字

        prompt['5']['inputs'].update({
            'batch_size': batch_size,
            'height': height,
            'width': width
        })
        prompt['22']['inputs']['text'] = text  # 正向提示词
        prompt['23']['inputs']['text'] = negative_text  # 反向提示词

        prompt['12']['inputs']['image'] = self.file_info['name']  # 图片

        weight = kwargs.get('img2img_info', {}).get('weight')
        if weight is not None:
            prompt['33']['inputs']['weight'] = weight
        self.prompt = prompt

    def get_i2i_styleRefer_prompt(self):
        """
        风格一致性 i2i_ipadapter.json
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')

        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['3']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        prompt['4']['inputs']['ckpt_name'] = model_name  # 模型名字

        prompt['5']['inputs'].update({
            'batch_size': batch_size,
            'height': height,
            'width': width
        })
        prompt['6']['inputs']['text'] = text  # 正向提示词
        prompt['7']['inputs']['text'] = negative_text  # 反向提示词

        prompt['12']['inputs']['image'] = self.file_info['name']  # 图片

        weight = kwargs.get('img2img_info', {}).get('weight')
        if weight is not None:
            prompt['14']['inputs']['weight'] = weight
        self.prompt = prompt

    def get_i2i_contentRefer_prompt(self):
        """
        i2i_line_controlnet.json
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')

        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE
        # height = kwargs.get('height')  # 角色一致性不支持更改宽高
        # width = kwargs.get('width')

        prompt['3']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        prompt['4']['inputs']['ckpt_name'] = model_name  # 模型名字

        prompt['76']['inputs'].update({
            'batch_size': batch_size,
            # 'height': height,
            # 'width': width
        })
        prompt['6']['inputs']['text'] = text  # 正向提示词
        prompt['7']['inputs']['text'] = negative_text  # 反向提示词

        prompt['67']['inputs']['image'] = self.file_info['name']  # 图片

        weight = kwargs.get('img2img_info', {}).get('weight')
        if weight is not None:
            prompt['15']['inputs']['strength'] = weight
        self.prompt = prompt

    def get_i2i_multiRefer_prompt(self):
        """
        i2i_multi.json 多图生图
        :return:
        """
        kwargs = self.extra_params
        img2img_info = self.extra_params.get('img2img_info', {})
        prompt = self.get_base_prompt()
        info = {}
        style_list = img2img_info.get('style_list', [])
        for item in style_list:
            _style = item.get('style')
            _weight = item.get('weight')
            _img_url = item.get('img_url')
            if not _style:
                continue
            info[_style] = {"style": _style, "weight": _weight, "img_url": _img_url}
        contentRefer_info = info.get('contentRefer', {})
        styleRefer_info = info.get('styleRefer', {})
        characterRefer_info = info.get('characterRefer', {})
        # 上传图片到comfy
        content_refer_info = self.upload_image_by_url(img_url=contentRefer_info.get('img_url'))
        style_refer_info = self.upload_image_by_url(img_url=styleRefer_info.get('img_url'))
        character_refer_info = self.upload_image_by_url(img_url=characterRefer_info.get('img_url'))

        if content_refer_info:
            prompt['1']['inputs']['image'] = content_refer_info['name']
            weight = contentRefer_info.get('weight')
            if weight is not None:
                prompt['6']['inputs']['strength'] = float(weight)
        else:
            # 没有内容一致性
            # prompt['6']['inputs']['strength'] = 0
            prompt['9']['inputs']['positive'][0] = "25"
            prompt['9']['inputs']['positive'][1] = 0

            prompt['9']['inputs']['negative'][0] = "21"
            prompt['9']['inputs']['negative'][1] = 0

        if style_refer_info:
            prompt['26']['inputs']['image'] = style_refer_info['name']
            weight = styleRefer_info.get('weight')
            if weight is not None:
                prompt['24']['inputs']['weight'] = float(weight)
        else:
            # 没有风格一致性
            prompt['24']['inputs']['model'][0] = '27'

        if character_refer_info:
            prompt['19']['inputs']['image'] = character_refer_info['name']
            weight = characterRefer_info.get('weight')
            if weight is not None:
                prompt['6']['inputs']['strength'] = float(weight)
        else:
            # 没有角色一致性
            prompt['9']['inputs']['model'][0] = '23'

        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')

        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['9']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        prompt['27']['inputs']['ckpt_name'] = model_name  # 模型名字

        prompt['28']['inputs'].update({
            'batch_size': batch_size,
            'height': height,
            'width': width
        })

        prompt['25']['inputs']['text'] = text  # 正向提示词
        prompt['21']['inputs']['text'] = negative_text  # 反向提示词

        self.prompt = prompt

    def get_i2i_openposeControl_prompt(self):
        """
        i2i_openpose_gen.json
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        # negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')

        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE
        # height = kwargs.get('height')  # 角色一致性不支持更改宽高
        # width = kwargs.get('width')

        prompt['9']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        prompt['8']['inputs']['ckpt_name'] = model_name  # 模型名字

        prompt['12']['inputs'].update({
            'batch_size': batch_size,
            # 'height': height,
            # 'width': width
        })
        prompt['7']['inputs']['text'] = text  # 正向提示词
        # prompt['10']['inputs']['text'] = negative_text  # 反向提示词

        prompt['1']['inputs']['image'] = self.file_info['name']  # 图片

        weight = kwargs.get('img2img_info', {}).get('weight')
        if weight is not None:
            prompt['4']['inputs']['strength'] = weight
        self.prompt = prompt

    def get_i2i_anime_v3_pose_prompt(self):
        """
        动漫V3 openpose生图 (使用 anime_v3_pose.json 工作流)
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt', '1girl, upper body, blone hair, long curl hair, jeans')
        negative_text = kwargs.get(
            'negative_prompt') or "(nsfw:0.7), (worst quality:1.5), (low quality:1.5), (normal quality:1.5), lowres,watermark, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, username, blurry, artist name"

        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        cfg = kwargs.get('cfg', 7.0)
        steps = kwargs.get('steps', 25)
        sampler_name = kwargs.get('sampler', 'euler_ancestral')
        scheduler = kwargs.get('scheduler', 'normal')
        denoise = kwargs.get('denoise', 1.0)
        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE

        # 设置基本生成参数 (对应anime_v3_pose.json的节点9)
        prompt['9']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        # 设置模型 (对应anime_v3_pose.json的节点8)
        prompt['8']['inputs']['ckpt_name'] = model_name

        # 设置批次大小 (对应anime_v3_pose.json的节点12)
        prompt['12']['inputs']['batch_size'] = batch_size

        # 设置提示词 (对应anime_v3_pose.json的节点7和10)
        prompt['7']['inputs']['text'] = text
        prompt['10']['inputs']['text'] = negative_text

        # 设置输入图片 (对应anime_v3_pose.json的节点1)
        prompt['1']['inputs']['image'] = self.file_info['name']

        # 设置ControlNet强度 (对应anime_v3_pose.json的节点4)
        weight = kwargs.get('img2img_info', {}).get('weight')
        if weight is not None:
            prompt['4']['inputs']['strength'] = weight

        self.prompt = prompt

    def run(self):
        try:
            self.get_prompt()
            self.queue_prompt()
            return True
        except Exception as e:
            error_logger.info(f"i2i gen prompt_id error {e}")
            return False


class FluxImg2ImgGenImageWorkflow(BaseComfy):

    def get_prompt(self, **kwargs):
        self.kwargs = kwargs
        img2img_info = self.extra_params.get('img2img_info', {})
        style = img2img_info.get('style', "")
        img_url = img2img_info.get('img_url', "")
        self.upload_image_by_url(img_url)
        func = getattr(self, f'get_i2i_{style}_prompt', None)
        if not func:
            raise ValueError(f"img2img style {style} not support")
        func()

    def get_i2i_characterRefer_prompt(self):
        """
        flux 角色一致性
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')
        # negative_text = kwargs.get('negative_prompt') or ""
        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['73']['inputs'].update({
            "seed": seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        prompt['86']['inputs'].update({
            'batch_size': batch_size,
            'height': height,
            'width': width
        })
        prompt['71']['inputs']['text'] = text  # 正向提示词
        # prompt['72']['inputs']['text'] = negative_text  # 反向提示词

        prompt['83']['inputs']['image'] = self.file_info['name']  # 图片

        prompt['70']['inputs']['ckpt_name'] = model_name  # 模型名字

        weight = kwargs.get('img2img_info', {}).get('weight')
        if weight is not None:
            prompt['87']['inputs']['weight'] = weight
        self.prompt = prompt

    def get_i2i_contentRefer_prompt(self):
        """
        flux i2i_line_controlnet.json
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        # negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')
        # prompt['23']['inputs']['ckpt_name'] = model_name  # 模型名字，因为使用模型不一致，暂时不传

        prompt['26']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        prompt['24']['inputs']['text'] = text  # 正向提示词
        # prompt['25']['inputs']['text'] = negative_text  # 反向提示词

        prompt['30']['inputs']['image'] = self.file_info['name']  # 图片

        self.prompt = prompt

    def run(self):
        try:
            self.get_prompt()
            self.queue_prompt()
            return True
        except Exception as e:
            error_logger.info(f"i2i gen prompt_id error {e}")
            return False


class FluxDevImg2ImgGenImageWorkflow(BaseComfy):

    def get_prompt(self, **kwargs):
        self.kwargs = kwargs
        img2img_info = self.extra_params.get('img2img_info', {})
        style = img2img_info.get('style', "")
        img_url = img2img_info.get('img_url', "")

        self.upload_image_by_url(img_url)
        func = getattr(self, f'get_i2i_{style}_prompt', None)
        if not func:
            raise ValueError(f"img2img style {style} not support")
        func()

    def get_i2i_characterRefer_prompt(self):
        """
        角色一致性 i2i_pulid.json
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')

        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['3']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            'denoise': denoise
        })

        prompt['4']['inputs']['ckpt_name'] = model_name  # 模型名字

        prompt['5']['inputs'].update({
            'batch_size': batch_size,
            'height': height,
            'width': width
        })
        prompt['22']['inputs']['text'] = text  # 正向提示词
        prompt['23']['inputs']['text'] = negative_text  # 反向提示词

        prompt['12']['inputs']['image'] = self.file_info['name']  # 图片

        weight = kwargs.get('img2img_info', {}).get('weight')
        if weight is not None:
            prompt['33']['inputs']['weight'] = weight
        self.prompt = prompt

    def run(self):
        try:
            self.get_prompt()
            self.queue_prompt()
            return True
        except Exception as e:
            error_logger.info(f"i2i gen prompt_id error {e}")
            return False


class GenAdvanceImageWorkflow(BaseComfy):

    def get_prompt(self, **kwargs):
        self.kwargs = kwargs
        _type = self.extra_params.get('task_type')
        gen_type = self.extra_params.get('gen_type', 'gen')

        if _type == 'gen_img_advance_lineart':
            # 线稿
            if gen_type == 'gen_and_hires_fix':
                self.get_gen_and_hires_fix_lineart_prompt()
            elif gen_type == 'hires_fix':
                self.get_hires_fix_lineart_prompt()
            else:
                self.get_lineart_prompt()
        elif _type in ['gen_img_advance_flux', 'gen_img_advance_flux_schnell']:
            if gen_type == 'gen':
                self.get_flux_schnell_prompt()
            elif gen_type == 'hires_fix':
                self.get_hires_fix_flux_art_prompt()
            else:
                raise ValueError

        elif _type in ['gen_img_advance_flux_art', 'gen_img_advance_art']:
            # art v1
            if gen_type == 'gen':
                self.get_flux_art_v1_prompt()
            elif gen_type == 'hires_fix':
                self.get_hires_fix_flux_art_prompt()
            else:
                raise ValueError

        elif _type == 'gen_img_advance_flux_dev':
            if gen_type == 'gen':
                self.get_flux_dev_prompt()
            elif gen_type == 'hires_fix':
                self.get_hires_fix_flux_dev_prompt()
            else:
                raise ValueError

        elif _type == 'gen_img_advance_flux_krea':
            if gen_type == 'gen':
                self.get_flux_krea_prompt()
            elif gen_type == 'hires_fix':
                self.get_hires_fix_flux_dev_prompt()
            else:
                raise ValueError

        elif _type == 'gen_img_advance_flux_dev_turbo':
            if gen_type == 'gen':
                self.get_flux_dev_turbo_prompt()
            else:
                raise ValueError

        elif _type == 'gen_img_advance_flux_schnell_turbo':
            if gen_type == 'gen':
                self.get_flux_schnell_turbo_prompt()
            else:
                raise ValueError

        elif _type == 'gen_img_advance_art_turbo':
            if gen_type == 'gen':
                self.get_art_turbo_prompt()
            else:
                raise ValueError

        elif _type == 'gen_img_advance_anime_v3':
            # 动漫 V3
            if gen_type == 'gen':
                self.get_anime_v3_prompt()
            elif gen_type == 'hires_fix':
                self.get_hires_fix_anime_v3_prompt()
            else:
                raise ValueError
        else:
            raise ValueError

    def get_lineart_prompt(self):
        """
        线稿
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')

        batch_size = kwargs.get('batch_size')
        height = kwargs.get('height')
        width = kwargs.get('width')

        if self.is_fast:
            # 快速生图模式
            sampler_name = self.fast_config.get('sampler_name', sampler_name)
            scheduler = self.fast_config.get('scheduler', scheduler)
            steps = self.fast_config.get('steps', steps)
            cfg = self.fast_config.get('cfg', cfg)
            prompt['101']['inputs']['strength_model'] = 1  # 快速生图lightling的lora系数
            prompt['101']['inputs']['strength_clip'] = 1

        prompt['79']['inputs']['string'] = text  # 正向提示词

        prompt['81']['inputs']['string'] = negative_text  # 反向提示词

        if cfg:
            prompt['84']['inputs']['float'] = cfg
        if seed:
            prompt['78']['inputs']['seed'] = seed
        if steps:
            prompt['85']['inputs']['int'] = steps
        if sampler_name:
            prompt['76']['inputs']['sampler_name'] = sampler_name
        if scheduler:
            prompt['77']['inputs']['scheduler'] = scheduler
        if width:
            prompt['82']['inputs']['int'] = width
        if height:
            prompt['83']['inputs']['int'] = height
        if batch_size:
            prompt['5']['inputs']['batch_size'] = batch_size
        self.prompt = prompt

    def get_gen_and_hires_fix_lineart_prompt(self):
        """
        线稿
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')

        batch_size = kwargs.get('batch_size')
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['79']['inputs']['string'] = text  # 正向提示词

        prompt['81']['inputs']['string'] = negative_text  # 反向提示词

        if cfg:
            prompt['84']['inputs']['float'] = cfg
        if seed:
            prompt['78']['inputs']['seed'] = seed
        if steps:
            prompt['85']['inputs']['int'] = steps
        if sampler_name:
            prompt['76']['inputs']['sampler_name'] = sampler_name
        if scheduler:
            prompt['77']['inputs']['scheduler'] = scheduler
        if width:
            prompt['82']['inputs']['int'] = width
        if height:
            prompt['83']['inputs']['int'] = height
        if batch_size:
            prompt['5']['inputs']['batch_size'] = batch_size

        extra_data = kwargs.get('extra_data', {})
        hd_fix_data = extra_data.get('hires_fix', {})
        hd_fix_switch = hd_fix_data.get('switch', False)
        if hd_fix_switch:
            hd_fix_denoise = hd_fix_data.get('denoise', 0.5)
            scale = hd_fix_data.get('scale', 1.5)
            upscale_method = hd_fix_data.get('upscale_method', 'lanczos')
            prompt['91']['inputs']['denoise'] = hd_fix_denoise
            prompt['101']['inputs']['upscale_method'] = upscale_method
            prompt['101']['inputs']['scale_by'] = scale / 4  # 默认使用的四倍超分
        self.prompt = prompt

    def get_hires_fix_lineart_prompt(self):
        """
        线稿超清修复
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['79']['inputs']['string'] = text  # 正向提示词

        prompt['81']['inputs']['string'] = negative_text  # 反向提示词

        if cfg:
            prompt['84']['inputs']['float'] = cfg
        if seed:
            prompt['78']['inputs']['seed'] = seed
        if steps:
            prompt['85']['inputs']['int'] = steps
        if sampler_name:
            prompt['76']['inputs']['sampler_name'] = sampler_name
        if scheduler:
            prompt['77']['inputs']['scheduler'] = scheduler
        if width:
            prompt['82']['inputs']['int'] = width
        if height:
            prompt['83']['inputs']['int'] = height
        if self.kwargs.get('file_name', ""):
            prompt['87']['inputs']['image'] = self.kwargs['file_name']

        scale = kwargs.get('scale', 1.5)
        upscale_method = kwargs.get('upscale_method', 'lanczos')
        prompt['90']['inputs']['upscale_method'] = upscale_method
        prompt['90']['inputs']['scale_by'] = scale / 4  # 默认使用的四倍超分

        hd_denoise = kwargs.get('hd_denoise')
        if hd_denoise is not None:
            prompt['3']['inputs']['denoise'] = hd_denoise  # 重绘幅度
        self.prompt = prompt

    def get_flux_schnell_prompt(self):
        """
        flux schnell 模型
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        seed = kwargs.get('seed')

        batch_size = kwargs.get('batch_size', 1)
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['6']['inputs']['text'] = text  # 正向提示词

        prompt['31']['inputs']['seed'] = seed  # seed
        prompt['27']['inputs'].update({
            'width': width,
            'height': height,
            'batch_size': batch_size
        })

        self.prompt = prompt

    def get_flux_dev_prompt(self):
        """
        flux dev 模型
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        seed = kwargs.get('seed')

        steps = kwargs.get('steps', 20)

        batch_size = kwargs.get('batch_size')
        height = kwargs.get('height')
        width = kwargs.get('width')
        cfg = kwargs.get('cfg')

        prompt['6']['inputs']['text'] = text  # 正向提示词
        prompt['6']['inputs']['steps'] = steps  # 步数  默认20
        prompt['35']['inputs']['guidance'] = cfg  # guidance  默认3.5 注意 flux dev的前端控制原本为cfg，但是dev比较特殊，cfg控制的是guidance，但是为了兼容前端还是用cfg传参

        prompt['31']['inputs']['seed'] = seed  # seed
        prompt['27']['inputs'].update({
            'width': width,
            'height': height,
            'batch_size': batch_size
        })

        self.prompt = prompt

    def get_flux_krea_prompt(self):
        """
        flux krea 模型
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        seed = kwargs.get('seed')

        steps = kwargs.get('steps', 20)

        batch_size = kwargs.get('batch_size')
        height = kwargs.get('height')
        width = kwargs.get('width')
        cfg = kwargs.get('cfg')

        prompt['229']['inputs']['text'] = text  # 正向提示词
        prompt['83']['inputs']['steps'] = steps  # 步数  默认20
        prompt['51']['inputs']['guidance'] = cfg  # guidance  默认3.5 注意 flux dev的前端控制原本为cfg，但是dev比较特殊，cfg控制的是guidance，但是为了兼容前端还是用cfg传参

        prompt['83']['inputs']['seed'] = seed  # seed
        prompt['235']['inputs'].update({
            'width': width,
            'height': height,
            'batch_size': batch_size
        })

        self.prompt = prompt

    def get_flux_art_v1_prompt(self):
        """
        flux art v1
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        seed = kwargs.get('seed')
        model_id = kwargs.get('model_id')
        from comfy.defines import model_info_dict

        model_name = model_info_dict.get(model_id, {}).get('model', '')

        if model_name:
            prompt['56']['inputs']['ckpt_name'] = model_name
        batch_size = kwargs.get('batch_size')
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['6']['inputs']['text'] = text  # 正向提示词

        prompt['59']['inputs']['seed'] = seed  # seed
        prompt['27']['inputs'].update({
            'width': width,
            'height': height,
            'batch_size': batch_size
        })

        self.prompt = prompt

    def get_hires_fix_flux_art_prompt(self):
        """
        Flux art超清修复
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()

        text = kwargs.get('prompt')
        seed = kwargs.get('seed', get_seed())
        scale = kwargs.get('scale', 1.5)

        prompt['91']['inputs']['seed'] = seed

        prompt['6']['inputs']['string'] = text  # 正向提示词

        if self.kwargs.get('file_name', ""):
            prompt['42']['inputs']['image'] = self.kwargs['file_name']

        prompt['89']['inputs']['scale_by'] = scale / 4  # 默认使用的四倍超分

        # hd_denoise = kwargs.get('hd_denoise')
        # if hd_denoise is not None:
        #     prompt['11']['inputs']['denoise'] = hd_denoise  # 重绘幅度
        self.prompt = prompt

    def get_hires_fix_flux_dev_prompt(self):
        """
        Flux Dev超清修复
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()

        text = kwargs.get('prompt')
        seed = kwargs.get('seed', get_seed())
        scale = kwargs.get('scale', 1.5)

        prompt['15']['inputs']['seed'] = seed

        prompt['3']['inputs']['string'] = text  # 正向提示词

        if self.kwargs.get('file_name', ""):
            prompt['18']['inputs']['image'] = self.kwargs['file_name']

        prompt['14']['inputs']['scale_by'] = scale / 4  # 默认使用的四倍超分

        hd_denoise = kwargs.get('hd_denoise')
        if hd_denoise is not None:
            prompt['15']['inputs']['denoise'] = hd_denoise  # 重绘幅度
        self.prompt = prompt

    def get_flux_dev_turbo_prompt(self):
        """
        flux dev turbo
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        seed = kwargs.get('seed')

        steps = kwargs.get('steps', 20)

        batch_size = kwargs.get('batch_size')
        height = kwargs.get('height')
        width = kwargs.get('width')
        cfg = kwargs.get('cfg')

        prompt['6']['inputs']['text'] = text  # 正向提示词
        prompt['17']['inputs']['steps'] = steps  # 步数  默认20
        prompt['68']['inputs'][
            'guidance'] = cfg  # guidance  默认3.5 注意 flux dev的前端控制原本为cfg，但是dev比较特殊，cfg控制的是guidance，但是为了兼容前端还是用cfg传参

        prompt['25']['inputs']['noise_seed'] = seed  # seed
        prompt['5']['inputs'].update({
            'width': width,
            'height': height,
            'batch_size': batch_size
        })

        self.prompt = prompt

    def get_flux_schnell_turbo_prompt(self):
        """
        flux art v1
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        seed = kwargs.get('seed')

        batch_size = kwargs.get('batch_size')
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['6']['inputs']['text'] = text  # 正向提示词

        prompt['25']['inputs']['noise_seed'] = seed  # seed
        prompt['5']['inputs'].update({
            'width': width,
            'height': height,
            'batch_size': batch_size
        })

        self.prompt = prompt

    def get_art_turbo_prompt(self):
        """
        flux art turbo v1
        :return:
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        seed = kwargs.get('seed')

        batch_size = kwargs.get('batch_size')
        height = kwargs.get('height')
        width = kwargs.get('width')

        prompt['6']['inputs']['text'] = text  # 正向提示词

        prompt['25']['inputs']['noise_seed'] = seed  # seed
        prompt['5']['inputs'].update({
            'width': width,
            'height': height,
            'batch_size': batch_size
        })

        self.prompt = prompt

    def get_anime_v3_prompt(self):
        """
        动漫 V3 基础生图
        """
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        model_id = kwargs.get('model_id')
        from comfy.defines import model_info_dict
        model_name = model_info_dict.get(model_id, {}).get('model', '')

        if model_name:
            prompt['7']['inputs']['ckpt_name'] = model_name
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        steps = kwargs.get('steps')
        # sampler_name = kwargs.get('sampler')
        # scheduler = kwargs.get('scheduler')
        denoise = kwargs.get('denoise')

        batch_size = kwargs.get('batch_size') or DEFAULT_BATCH_SIZE
        height = kwargs.get('height')
        width = kwargs.get('width')

        # 更新 KSampler 节点 (节点10)
        prompt['10']['inputs'].update({
            'seed': seed,
            'steps': steps,
            'cfg': cfg,
            # 'sampler_name': sampler_name,
            # 'scheduler': scheduler,
            'denoise': denoise
        })

        # 更新提示词节点
        prompt['15']['inputs']['text'] = text  # 正向提示词 (节点15)
        prompt['8']['inputs']['text'] = negative_text  # 负向提示词 (节点8)

        # 更新潜在图像节点 (节点14)
        prompt['14']['inputs'].update({
            'batch_size': batch_size,
            'height': height,
            'width': width
        })
        self.prompt = prompt

    def get_hires_fix_anime_v3_prompt(self):
        kwargs = self.extra_params
        prompt = self.get_base_prompt()
        text = kwargs.get('prompt')
        if self.model_name:
            model_name = self.model_name
        else:
            model_name = kwargs.get('model_name')
        seed = kwargs.get('seed')
        negative_text = kwargs.get('negative_prompt') or ""
        cfg = kwargs.get('cfg')
        sampler_name = kwargs.get('sampler')
        scheduler = kwargs.get('scheduler')
        hd_denoise = kwargs.get('hd_denoise')  # 重绘幅度

        prompt['20']['inputs'].update({
            'seed': seed,
            'steps': 12,
            'cfg': cfg,
            'sampler_name': sampler_name,
            'scheduler': scheduler,
            # 'denoise': denoise
        })

        prompt['6']['inputs']['text'] = text  # 正向提示词
        prompt['7']['inputs']['text'] = negative_text  # 反向提示词

        prompt['4']['inputs']['ckpt_name'] = model_name  # 模型名字
        scale = kwargs.get('scale', 1.5)  # 超分倍数
        upscale_method = kwargs.get('upscale_method', 'lanczos')
        prompt['16']['inputs']['upscale_method'] = upscale_method
        prompt['16']['inputs']['scale_by'] = scale / 4
        filename = self.kwargs.get('file_name')  # 文件上传后在comfyui的文件名
        prompt['24']['inputs']['image'] = filename
        if hd_denoise is not None:
            prompt['20']['inputs']['denoise'] = hd_denoise
        self.prompt = prompt


class Img2TextWorkflow(Image2ImageWorkflow):
    """
    提示词反推
    """

    def get_prompt(self, *, file_name):
        prompt = self.get_base_prompt()
        params = self.extra_params
        prompt['4']['inputs']['image'] = file_name
        _type = params.get('type', 'Descriptive')
        prompt['7']['inputs']['caption_type'] = _type
        self.prompt = prompt


class AnimeV3InpaintWorkflow(Image2ImageWorkflow):
    """
    动漫 V3 局部重绘 (inpaint)
    """

    def get_prompt(self, *, file_name, mask_file_name):
        params = self.extra_params
        denoise = params.get('denoise')
        prompt = self.get_base_prompt()
        prompt['71']['inputs']['image'] = mask_file_name
        prompt['11']['inputs']['image'] = file_name
        text = params.get('prompt')  # 正向提示词

        if denoise is not None:
            prompt['3']['inputs']['denoise'] = denoise
        prompt['3']['inputs']['seed'] = get_seed()

        prompt['6']['inputs']['text'] = text
        local_redraw_model_id = params.get('local_redraw_model_id')

        from comfy.defines import model_info_dict

        model_name = model_info_dict.get(local_redraw_model_id, {}).get('model', '')

        local_redraw_config = model_info_dict.get(local_redraw_model_id, {}).get('local_redraw_config', {})
        negative_prompt = local_redraw_config.pop('negative_prompt', None)
        strength = local_redraw_config.pop('strength', None)
        if negative_prompt is not None:
            prompt['7']['inputs']['text'] = negative_prompt

        if strength is not None:
            prompt['15']['inputs']['strength'] = strength

        if local_redraw_config:
            prompt['3']['inputs'].update(local_redraw_config)

        if model_name:
            prompt['75']['inputs']['ckpt_name'] = model_name  # V3 inpaint 使用节点75加载模型
        self.prompt = prompt

    def run(self):
        try:
            img_url = self.extra_params.get('img_url')
            self.upload_image_by_url(img_url)
            file_name = self.file_info['name']
            mask_img_url = self.extra_params.get('mask_img_url')
            self.upload_image_by_url(mask_img_url)
            mask_file_name = self.file_info['name']
            self.get_prompt(file_name=file_name, mask_file_name=mask_file_name)
            self.queue_prompt()  # 加任务
            return True
        except Exception as e:
            error_logger.info(f"anime v3 inpaint gen prompt_id error {e}")
            return False


class AnimeV3OutpaintWorkflow(Image2ImageWorkflow):
    """
    动漫 V3 扩图 (outpaint)
    """

    def get_prompt(self, *, file_name):
        params = self.extra_params
        prompt = self.get_base_prompt()
        text = params.get('prompt')  # 正向提示词
        # batch_size = params.get('batch_size', 1)  # 目前仅支持一张
        left = params.get('left', 200)
        right = params.get('right', 200)
        top = params.get('top', 0)
        bottom = params.get('bottom', 0)
        denoise = params.get('denoise')
        seed = params.get('seed', get_seed())
        prompt['11']['inputs']['image'] = file_name

        if denoise is not None:
            prompt['3']['inputs']['denoise'] = denoise
        prompt['3']['inputs']['seed'] = seed

        prompt['6']['inputs']['text'] = text
        # enlarge_image_model_id = params.get('enlarge_image_model_id')

        from comfy.defines import model_info_dict

        # model_name = model_info_dict.get(enlarge_image_model_id, {}).get('model', '')
        # if model_name:
        #     prompt['4']['inputs']['ckpt_name'] = model_name  # V3 outpaint 使用节点4加载模型
        # enlarge_img_config = model_info_dict.get(enlarge_image_model_id, {}).get('enlarge_img_config', {})
        # enlarge_img_config_copy = copy.deepcopy(enlarge_img_config)
        # negative_prompt = enlarge_img_config_copy.pop('negative_prompt', "")
        # strength = enlarge_img_config_copy.pop('strength', None)

        # if strength is not None:
        #     prompt['15']['inputs']['strength'] = strength

        # prompt['3']['inputs'].update(enlarge_img_config_copy)
        # prompt['7']['inputs']['text'] = negative_prompt

        prompt['10']['inputs'].update({
            "left": left,
            "right": right,
            "top": top,
            "bottom": bottom,
        })

        self.prompt = prompt


class TextToVideoWorkflow(BaseComfy):
    """
    文生视频工作流
    """

    def get_prompt(self, **kwargs):
        params = self.extra_params
        prompt = self.get_base_prompt()

        # 获取参数
        text = params.get('prompt', '')  # 正向提示词
        negative_text = params.get('negative_prompt', '')  # 反向提示词
        seed = params.get('seed', get_seed())
        width = params.get('width', 832)
        height = params.get('height', 480)
        duration = params.get('duration', 3)  # 时长(秒)
        steps = params.get('steps', 10)
        cfg = params.get('cfg', 1.0)
        sampler_name = params.get('sampler', 'euler')
        scheduler = params.get('scheduler', 'beta')

        # 设置提示词 (对应texttovideo.json的节点21和7)
        prompt['21']['inputs']['text'] = text
        prompt['7']['inputs']['text'] = negative_text

        # 设置随机种子 (对应texttovideo.json的节点16)
        prompt['16']['inputs']['noise_seed'] = seed

        # 设置视频尺寸 (对应texttovideo.json的节点44和45)
        prompt['44']['inputs']['value'] = width
        prompt['45']['inputs']['value'] = height

        # 设置视频时长 (对应texttovideo.json的节点43)
        prompt['43']['inputs']['value'] = duration

        # 设置采样参数 (对应texttovideo.json的节点4, 5, 6)
        prompt['4']['inputs']['sampler_name'] = sampler_name
        prompt['5']['inputs']['scheduler'] = scheduler
        prompt['5']['inputs']['steps'] = steps
        prompt['6']['inputs']['cfg'] = cfg

        self.prompt = prompt


class ImageToVideoWorkflow(BaseComfy):
    """
    图生视频工作流
    """

    def get_prompt(self, **kwargs):
        params = self.extra_params
        prompt = self.get_base_prompt()

        # 获取参数
        text = params.get('prompt', '')  # 正向提示词
        negative_text = params.get('negative_prompt', '')  # 反向提示词
        seed = params.get('seed', get_seed())
        frames = params.get('frames', 49)  # 视频帧数
        steps = params.get('steps', 10)
        cfg = params.get('cfg', 1.0)
        sampler_name = params.get('sampler', 'uni_pc')
        scheduler = params.get('scheduler', 'simple')

        # 设置提示词 (对应imgtovideo.json的节点6和7)
        prompt['6']['inputs']['text'] = text
        prompt['7']['inputs']['text'] = negative_text

        # 设置随机种子 (对应imgtovideo.json的节点67)
        prompt['67']['inputs']['noise_seed'] = seed

        # 设置视频帧数 (对应imgtovideo.json的节点78)
        prompt['78']['inputs']['value'] = frames

        # 设置采样参数 (对应imgtovideo.json的节点68, 71, 65)
        prompt['68']['inputs']['sampler_name'] = sampler_name
        prompt['71']['inputs']['scheduler'] = scheduler
        prompt['71']['inputs']['steps'] = steps
        prompt['65']['inputs']['cfg'] = cfg

        # 设置输入图片 (对应imgtovideo.json的节点52)
        # 图片文件名将在run方法中上传后设置

        self.prompt = prompt

    def run(self):
        try:
            img_url = self.extra_params.get('img_url')
            if not img_url:
                return False

            # 上传图片
            self.upload_image_by_url(img_url)
            file_name = self.file_info['name']

            # 设置图片文件名到工作流
            self.get_prompt()
            self.prompt['52']['inputs']['image'] = file_name

            # 提交任务
            self.queue_prompt()
            return True
        except Exception as e:
            error_logger.info(f"image to video gen prompt_id error {e}")
            return False


if __name__ == '__main__':
    ...
