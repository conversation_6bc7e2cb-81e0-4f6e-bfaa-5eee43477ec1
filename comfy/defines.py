from comfy.apis import GenImageWorkflow, RmbgWorkflow, GenAdvanceImageWorkflow, \
    Img2ImgGenImageWorkflow, ReRedrawWorkflow, EnlargeImageWorkflow, LineRecolorWorkflow, ImgVaryWorkflow, \
    ImgVaryWdWorkflow, FluxDevEnlargeImageWorkflow, FluxDevReRedrawWorkflow, FluxImg2ImgGenImageWorkflow, \
    UpscaleWorkflow, Img2TextWorkflow, OpenposeWorkflow, DepthProcessWorkflow, CannyProcessWorkflow, \
    AnimeV3InpaintWorkflow, AnimeV3OutpaintWorkflow

upscale_workflow_map = {
    'upscale_normal': {
        'obj': UpscaleWorkflow,
        'model': 'RealESRGAN_x4plus.pth',
        'workflow_file': 'upscale.json',
        # 'workflow_file': 'upscale_new.json',
        'desc': '放大'
    },
    'upscale_anime': {
        'obj': UpscaleWorkflow,
        'model': 'RealESRGAN_x4plus_anime_6B.pth',
        'workflow_file': 'upscale.json',
        'desc': '放大'
    }
}

rmbg_workflow_map = {
    'rmbg_general': {
        'obj': RmbgWorkflow,
        'model': 'u2net',
        'workflow_file': 'rmbg.json',
        'desc': '基本物体去背景'
    }
}

# 重绘
local_redraw_map = {
    'local_redraw': {
        'obj': ReRedrawWorkflow,
        'model': '',
        'workflow_file': 'local_redraw.json',
        'desc': 'SDXL局部重绘'
    },
    'flux_dev_redraw': {
        'obj': FluxDevReRedrawWorkflow,
        'model': '',
        'workflow_file': 'flux_dev_redraw.json',
        'desc': 'FluxDev局部重绘'
    },
    'anime_v3_inpaint': {
        'obj': AnimeV3InpaintWorkflow,
        'model': '',
        'workflow_file': 'anime_v3_inpaint.json',
        'desc': '动漫V3局部重绘'
    }
}

# 扩图
enlarge_image_map = {
    'enlarge_image': {
        'obj': EnlargeImageWorkflow,
        'model': '',
        'workflow_file': 'enlarge_image_new.json',
        'desc': 'SDXL扩图'
    },
    'flux_dev_enlarge_image': {
        'obj': FluxDevEnlargeImageWorkflow,
        'model': '',
        'workflow_file': 'flux_dev_enlarge.json',
        'desc': 'FluxDev扩图'
    },
    'anime_v3_outpaint': {
        'obj': AnimeV3OutpaintWorkflow,
        'model': '',
        'workflow_file': 'anime_v3_outpaint.json',
        'desc': '动漫V3扩图'
    }
}

line_recolor_map = {
    'line_recolor': {
        'obj': LineRecolorWorkflow,
        'model': '',
        'workflow_file': 'line_rcolor.json',
        'desc': '线稿上色'
    }
}

img_vary_map = {
    'img_vary': {
        'obj': ImgVaryWorkflow,
        'model': '',
        'workflow_file': 'img_vary.json',
        'desc': '图片变化'
    },
    'img_vary_wd': {
        'obj': ImgVaryWdWorkflow,
        'model': '',
        'workflow_file': 'img_vary_wd.json',
        'desc': '图片变化-提示词反推'
    }
}
img2text_map = {
    'img2text': {
        'obj': Img2TextWorkflow,
        'model': '',
        'workflow_file': 'img2text.json',
        'desc': '提示词反推'
    }
}

image_control_pre_process_map = {
    'depth_process': {
        'obj': DepthProcessWorkflow,
        'model': 'depth_anything_vitl14.pth',
        'workflow_file': 'depth_process.json',
        'desc': 'depth预处理任务'
    },
    'canny_process': {
        'obj': CannyProcessWorkflow,
        'model': '',
        'workflow_file': 'canny_process.json',
        'desc': 'canny预处理任务'
    },
    'openpose_process': {
        'obj': OpenposeWorkflow,
        'model': '',
        'workflow_file': 'openpose_process.json',
        'desc': 'pose预处理任务'
    }
}


workflow_map = {}
workflow_map.update(upscale_workflow_map)
workflow_map.update(rmbg_workflow_map)
workflow_map.update(local_redraw_map)
workflow_map.update(enlarge_image_map)
workflow_map.update(line_recolor_map)
workflow_map.update(img_vary_map)
workflow_map.update(img2text_map)
workflow_map.update(image_control_pre_process_map)

model_info_dict = {

    # piclumen 真实感 V2
    '34ec1b5a-8962-4a93-b047-68cec9691dc2': {
        'obj': GenImageWorkflow,
        'img2img_obj': Img2ImgGenImageWorkflow,
        'type': 'gen_img_normal',
        'model': 'PicLumen_HXL_Real_v2.safetensors',
        'md5': "e02bf4517d8fc49a08a4a89865bd85e9",
        'model_display': 'PicLumen Realistic V2',
        'desc': 'Focuses on creating highly realistic images with fine details.',
        'status': 1,
        'order': 2,
        'workflow_file': {
            "gen": 'img_base_gen.json',
            'hires_fix': 'img_base_upscale.json',
        },
        # 图生图配置
        'img2img_workflow_info': {
            # 线条绘图
            'contentRefer': {
                'sort': 1,
                "file": 'i2i_line_controlnet.json',
                'default_weight': 0.5,
                'label': "Content Ref",
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_content.jpg",
                'default_conf': {
                    'sampler_name': "dpm_2",
                    'scheduler': "normal",
                    'cfg': 4.5,
                    'steps': 30,
                }
            },
            # 风格绘图
            'styleRefer': {
                'sort': 2,
                "file": 'i2i_ipadapter.json',
                'default_weight': 0.95,
                'label': "Style Ref",
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_style.jpg",
                'default_conf': {
                    'sampler_name': "dpm_2",
                    'scheduler': "normal",
                    'cfg': 4.5,
                    'steps': 30,
                }
            },
            # 人脸绘图
            'characterRefer': {
                'sort': 3,
                "file": 'i2i_pulid.json',
                'default_weight': 0.7,
                'label': 'Character Ref',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "dpm_2",
                    'scheduler': "karras",
                    'cfg': 4.5,
                    'steps': 25,
                }
            },
            # 多图生图
            'multiRefer': {
                'sort': 4,
                "file": 'i2i_multi.json',
                'default_weight': 0.5,
                'label': 'Multi Refer',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "dpmpp_2m_sde_gpu",
                    'scheduler': "karras",
                    'cfg': 4.5,
                    'steps': 25,
                }
            },
            # openpose绘图
            'openposeControl': {
                'sort': 5,
                "file": 'i2i_openpose_gen.json',
                'default_weight': 0.8,
                'label': 'openpose',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "euler_ancestral",
                    'scheduler': "normal",
                    'cfg': 7,
                    'steps': 25,
                }
            },
        },
        # 扩图默认配置
        'enlarge_img_config': {
            'sampler_name': 'dpmpp_2m_sde_gpu',
            'scheduler': 'karras',
            'steps': 25,
            'cfg': 4.5,
            'negative_prompt': "text, watermark, (frame:1.8), person, people, illustration",
        },
        # 图片变化默认配置
        'img_vary_config': {
            'sampler_name': 'dpmpp_2m_sde_gpu',
            'scheduler': 'karras',
            'steps': 25,
            'cfg': 4.5
        },
        # 默认配置
        'default_config': {
            'width': 1024,
            'height': 1024,
            "seed": -1,
            "steps": 25,
            "cfg": 4.5,
            "samplerName": "dpmpp_2m_sde_gpu",
            "scheduler": "karras",
            "denoise": 1.0,
            "positivePrompt": "",
            "negativePrompt": "NSFW, watermark",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2FPicLumen%20Realistic%20V2.png",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },

    # piclumen Art V1
    '23887bba-507e-4249-a0e3-6951e4027f2b': {
        'obj': GenAdvanceImageWorkflow,
        'img2img_obj': FluxImg2ImgGenImageWorkflow,
        'type': 'gen_img_advance_flux_art',
        'model': 'PicLumen_Schnell_Art_v1.safetensors',
        'md5': 'a2e7e60252d47a38f3a44612e4e050e3',
        'model_display': 'PicLumen Art V1',
        'desc': 'Tailored for generating anime images with a unique artistic touch.',
        'status': 1,
        'order': 1,
        'workflow_file': {
            "gen": 'flux_art_v1.json',
            'hires_fix': 'flux_art_upscale.json',
        },
        # 图生图配置
        'img2img_workflow_info': {
            # 人脸绘图
            'characterRefer': {
                'sort': 3,
                "file": 'i2i_flux_pulid.json',
                'default_weight': 0.8,
                'label': 'Character Ref',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "euler",
                    'scheduler': "simple",
                    'cfg': 1,
                    'steps': 6,
                }
            },

        },
        'default_config': {
            "seed": -1,
            "steps": 6,
            'width': 1024,
            'height': 1024,
            "cfg": 1.0,
            "samplerName": "euler",
            "scheduler": "normal",
            "denoise": 1.0,
            "positivePrompt": "",
            "negativePrompt": "",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2FPicLumen%20Anime%20V2.png",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },

    # piclumen 动漫 V2
    'cb4af9c7-41b0-47d3-944a-221446c7b8bc': {
        'obj': GenImageWorkflow,
        'img2img_obj': Img2ImgGenImageWorkflow,
        'type': 'gen_img_normal',
        'model': 'PicLumen_HXL_Anime_v2.safetensors',
        'md5': '2641d631f8b27130aea8084aa04ef71a',
        'model_display': 'PicLumen Anime V2',
        'desc': 'Tailored for generating anime images with a unique artistic touch.',
        'status': 1,
        'order': 3,
        'workflow_file': {
            "gen": 'img_base_gen.json',
            'hires_fix': 'img_base_upscale.json',
        },
        'img2img_workflow_info': {
            # 线条绘图
            'contentRefer': {
                'sort': 1,
                "file": 'i2i_line_controlnet.json',
                'default_weight': 0.5,
                'label': "Content Ref",
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_content.jpg",
                'default_conf': {
                    'sampler_name': "euler",
                    'scheduler': "karras",
                    'cfg': 7,
                    'steps': 25,
                }
            },
            # 风格绘图
            'styleRefer': {
                'sort': 2,
                "file": 'i2i_ipadapter.json',
                'default_weight': 0.95,
                'label': "Style Ref",
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_style.jpg",
                'default_conf': {
                    'sampler_name': "euler",
                    'scheduler': "karras",
                    'cfg': 7,
                    'steps': 25,
                }
            },
            # 人脸绘图
            'characterRefer': {
                'sort': 3,
                "file": 'i2i_pulid.json',
                'default_weight': 0.7,
                'label': 'Character Ref',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "euler_ancestral",
                    'scheduler': "karras",
                    'cfg': 7,
                    'steps': 25,
                }
            },
            # 多图生图
            'multiRefer': {
                'sort': 4,
                "file": 'i2i_multi.json',
                'default_weight': 0.5,
                'label': 'Multi Refer',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "dpmpp_2m_sde_gpu",
                    'scheduler': "karras",
                    'cfg': 4.5,
                    'steps': 25,
                }
            },
            # openpose绘图
            'openposeControl': {
                'sort': 5,
                "file": 'i2i_openpose_gen.json',
                'default_weight': 0.8,
                'label': 'openpose',
                "avatar": "",
                'default_conf': {
                    'sampler_name': "euler_ancestral",
                    'scheduler': "normal",
                    'cfg': 7,
                    'steps': 25,
                }
            }
        },
        'enlarge_img_config': {
            'sampler_name': 'euler',
            'scheduler': 'karras',
            'steps': 25,
            'cfg': 7,
            'negative_prompt': "(nsfw:0.7), (worst quality:1.5), (low quality:1.5), (normal quality:1.5), lowres,watermark, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, username, blurry, artist name"
        },
        # 图片变化默认配置
        'img_vary_config': {
            'sampler_name': 'euler_ancestral',
            'scheduler': 'karras',
            'steps': 25,
            'cfg': 7
        },
        'default_config': {
            "seed": -1,
            "steps": 25,
            'width': 1024,
            'height': 1024,
            "cfg": 7.0,
            "samplerName": "euler_ancestral",
            "scheduler": "karras",
            "denoise": 1.0,
            "positivePrompt": "",
            "negativePrompt": "(nsfw:0.7), (worst quality:1.5), (low quality:1.5), (normal quality:1.5), lowres,watermark, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, username, blurry, artist name",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2FPicLumen%20Anime%20V2.png",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },

    # piclumen 动漫 V3
    'f87a123e-4b5c-4d6e-b7a1-2c3d4e5f6a7b': {
        'obj': GenAdvanceImageWorkflow,
        'img2img_obj': Img2ImgGenImageWorkflow,
        'type': 'gen_img_advance_anime_v3',
        'model': 'animagineXL40_v4Opt.safetensors',
        'md5': 'a2e7e60252d47a38f3a44612e4e050e3',
        'model_display': 'PicLumen Anime V3',
        'desc': 'Advanced anime model with enhanced quality and artistic style.',
        'status': 1,
        'order': 3,
        'workflow_file': {
            "gen": 'anime_v3_base.json',
            'hires_fix': 'img_base_upscale.json',
        },
        'img2img_workflow_info': {
            # 线条绘图
            'contentRefer': {
                'sort': 1,
                "file": 'i2i_line_controlnet.json',
                'default_weight': 0.5,
                'label': "Content Ref",
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_content.jpg",
                'default_conf': {
                    'sampler_name': "euler",
                    'scheduler': "karras",
                    'cfg': 7,
                    'steps': 25,
                }
            },
            # 风格绘图
            'styleRefer': {
                'sort': 2,
                "file": 'i2i_ipadapter.json',
                'default_weight': 0.95,
                'label': "Style Ref",
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_style.jpg",
                'default_conf': {
                    'sampler_name': "euler",
                    'scheduler': "karras",
                    'cfg': 7,
                    'steps': 25,
                }
            },
            # 人脸绘图
            'characterRefer': {
                'sort': 3,
                "file": 'i2i_pulid.json',
                'default_weight': 0.7,
                'label': 'Character Ref',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "euler_ancestral",
                    'scheduler': "karras",
                    'cfg': 7,
                    'steps': 25,
                }
            },
            # openpose绘图
            'anime_v3_pose': {
                'sort': 5,
                "file": 'anime_v3_pose.json',
                'default_weight': 0.8,
                'label': 'openpose',
                "avatar": "",
                'default_conf': {
                    'sampler_name': "euler_ancestral",
                    'scheduler': "normal",
                    'cfg': 7,
                    'steps': 25,
                }
            }
        },
        'enlarge_img_config': {
            'enlarge_type': "anime_v3_outpaint",
            'sampler_name': 'dpmpp_2m_sde_gpu',
            'scheduler': 'karras',
            'steps': 25,
            'cfg': 4.5,
            'negative_prompt': "text, watermark, (frame:1.8), person, people, illustration"
        },
        # 局部重绘配置
        'local_redraw_config': {
            'redraw_type': "anime_v3_inpaint",
            'sampler_name': 'euler_ancestral',
            'scheduler': 'simple',
            'steps': 25,
            'cfg': 7,
            'strength': 0.5,
            'negative_prompt': "(bad quality,worst quality,low quality,bad anatomy,bad hand:1.3), nsfw, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, artist name"
        },
        'default_config': {
            "seed": -1,
            "steps": 20,
            'width': 1024,
            'height': 1024,
            "cfg": 3.0,
            "samplerName": "euler_ancestral",
            "scheduler": "simple",
            "denoise": 1.0,
            "positivePrompt": "",
            "negativePrompt": "(bad quality,worst quality,low quality,bad anatomy,bad hand:1.3), nsfw, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, artist name, moniors, little girls, young age",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2FPicLumen%20Anime%20V3.png",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },

    # piclumen Lineart V1
    '2f0d593a-47db-42e6-b90b-e4534df65a98': {
        'obj': GenAdvanceImageWorkflow,
        'type': 'gen_img_advance_lineart',
        'model': 'PicLumen_HXL_Anime_v2.safetensors',
        'model_display': 'PicLumen Lineart V1',
        'desc': 'Finetuned from Animagine, produces high-quality black-and-white sketches with clean lines and detailed features.',
        'status': 1,
        'order': 4,
        'workflow_file': {
            "gen": 'lineart_xl_v1.json',
            "hires_fix": 'lineart_xl_v1_upscale.json'
        },
        'img2img_workflow_info': {
        },
        'default_config': {
            "seed": -1,
            "steps": 22,
            'width': 1024,
            'height': 1024,
            "cfg": 5.0,
            "samplerName": "euler_ancestral",
            "scheduler": "karras",
            "denoise": 1.0,
            "positivePrompt": "",
            "negativePrompt": "nsfw, lowres, (bad), text, error, fewer, extra, missing, worst quality, jpeg artifacts, low quality, watermark, unfinished, displeasing, oldest, early, chromatic aberration, signature, extra digits, artistic error, username, scan, [abstract]",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2FPicLumen%20Lineart%20V1.png",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },

    # Pony V6
    '14a399de-69d9-4e3b-961d-e95b35853557': {
        'obj': GenImageWorkflow,
        'img2img_obj': Img2ImgGenImageWorkflow,
        'type': 'gen_img_normal',
        'model': 'PonyDerivation_v1.safetensors',
        'md5': '9050c4974b2cc0e71a383eb62b629ccc',
        'model_display': 'Pony Diffusion V6',
        'desc': 'Pony Derivation v1.0',
        'status': 1,
        'order': 5,
        'workflow_file': {
            "gen": 'img_base_gen.json',
            'hires_fix': 'img_base_upscale.json',
        },
        'img2img_workflow_info': {
            # 线条绘图
            'contentRefer': {
                'sort': 1,
                "file": 'i2i_line_controlnet.json',
                'default_weight': 0.45,
                'label': "Content Ref",
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_content.jpg",
                'default_conf': {
                    'sampler_name': "euler_ancestral",
                    'scheduler': "karras",
                    'cfg': 6,
                    'steps': 25,
                }
            },
            # 风格绘图
            'styleRefer': {
                'sort': 2,
                "file": 'i2i_ipadapter.json',
                'default_weight': 0.95,
                'label': "Style Ref",
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_style.jpg",
                'default_conf': {
                    'sampler_name': "euler_ancestral",
                    'scheduler': "karras",
                    'cfg': 6,
                    'steps': 25,
                }
            },
            # 人脸绘图
            'characterRefer': {
                'sort': 3,
                "file": 'i2i_pulid.json',
                'default_weight': 0.7,
                'label': 'Character Ref',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "euler_ancestral",
                    'scheduler': "karras",
                    'cfg': 6,
                    'steps': 25,
                }
            },
            # openpose绘图
            'openposeControl': {
                'sort': 5,
                "file": 'i2i_openpose_gen.json',
                'default_weight': 0.8,
                'label': 'openpose',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "euler_ancestral",
                    'scheduler': "normal",
                    'cfg': 7,
                    'steps': 25,
                }
            },
        },
        'enlarge_img_config': {
            'sampler_name': 'euler_ancestral',
            'scheduler': 'karras',
            'steps': 25,
            'cfg': 6,
            'strength': 0.6,
            'negative_prompt': ""
        },
        # 图片变化默认配置
        'img_vary_config': {
            'sampler_name': 'euler_ancestral',
            'scheduler': 'karras',
            'steps': 25,
            'cfg': 6
        },
        'local_redraw_config': {
            'sampler_name': 'euler_ancestral',
            'scheduler': 'karras',
            'steps': 25,
            'cfg': 6,
            'strength': 0.45,
            'negative_prompt': ""
        },
        'line_recolor_config': {
            'sampler_name': 'euler_ancestral',
            'scheduler': 'karras',
            'negative_prompt': "simple-toned,monochrome,colorless"
        },

        'default_config': {
            "seed": -1,
            "steps": 25,
            'width': 1024,
            'height': 1024,
            "cfg": 7.0,
            "samplerName": "euler_ancestral",
            "scheduler": "karras",
            "denoise": 1.0,
            "positivePrompt": "",
            "negativePrompt": "",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2FPicLumen%20Anime%20V2.png",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },

    # flux schnell
    'e40d01af-dec2-49dd-8944-f2aae4ba0b05': {
        'obj': GenAdvanceImageWorkflow,
        'img2img_obj': FluxImg2ImgGenImageWorkflow,
        'type': 'gen_img_advance_flux_schnell',
        'model': 'flux1-schnell-fp8.safetensors',
        'model_display': 'FLUX.1-schnell',
        'desc': 'Original FLUX.1-schnell model from black forest lab',
        'status': 1,
        'order': 6,
        'workflow_file': {
            "gen": 'flux_schnell.json',
            'hires_fix': 'flux_art_upscale.json',
        },
        # 图生图配置
        'img2img_workflow_info': {
            # 人脸绘图
            'characterRefer': {
                'sort': 3,
                "file": 'i2i_flux_pulid.json',
                'default_weight': 0.8,
                'label': 'Character Ref',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "euler",
                    'scheduler': "simple",
                    'cfg': 1,
                    'steps': 6,
                }
            },

        },
        'default_config': {
            "seed": -1,
            "steps": 4,
            'width': 1024,
            'height': 1024,
            "cfg": 5.0,
            "samplerName": "euler_ancestral",
            "scheduler": "karras",
            "denoise": 1.0,
            "positivePrompt": "",
            "negativePrompt": "",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fflux.1-schnell.webp",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },

    # flux dev
    '08e4f71a-416d-4f1e-a853-6b0178af1f09': {
        'obj': GenAdvanceImageWorkflow,
        'img2img_obj': FluxImg2ImgGenImageWorkflow,
        'type': 'gen_img_advance_flux_dev',
        'model': 'FluxD_Mix_v1.safetensors',
        'model_display': 'FLUX.1-dev',
        'desc': 'High quality model from Black Forest Labs, can work with FLUX tools to achieve high-quality output',
        'status': 1,
        'order': 7,
        'workflow_file': {
            "gen": 'flux_dev.json',
            "hires_fix": 'flux_dev_upscale.json',
        },
        'img2img_workflow_info': {
        #     # 线条绘图
        #     'contentRefer': {
        #         'sort': 1,
        #         "file": 'i2i_flux_line_controlnet_canny_model.json',
        #         'default_weight': 0.5,
        #         'label': "Content Ref",
        #         "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_content.jpg",
        #         'default_conf': {
        #             'sampler_name': "euler",
        #             'scheduler': "simple",
        #             'cfg': 1,
        #             'steps': 30,
        #         }
        #     },
            # 人脸绘图
            'characterRefer': {
                'sort': 3,
                "file": 'i2i_flux_pulid.json',
                'default_weight': 0.8,
                'label': 'Character Ref',
                "avatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fref_character.jpg",
                'default_conf': {
                    'sampler_name': "euler",
                    'scheduler': "simple",
                    'cfg': 1,
                    'steps': 20,
                }
            },
        },
        # 扩图默认配置
        'enlarge_img_config': {
            'enlarge_type': "flux_dev_enlarge_image"
        },
        # 重绘配置
        'local_redraw_config': {
            'redraw_type': "flux_dev_redraw",
        },
        'default_config': {
            "seed": -1,
            "steps": 20,
            'width': 1024,
            'height': 1024,
            "cfg": 3.5,
            "samplerName": "euler_ancestral",
            "scheduler": "karras",
            "denoise": 1.0,
            "positivePrompt": "",
            "negativePrompt": "",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fflux.1-schnell.webp",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },
    # flux krea
    'krea87d6-5e4f-3a2b-1c0d-9e8f7g6h5i4j': {
        'obj': GenAdvanceImageWorkflow,
        'type': 'gen_img_advance_flux_krea',
        'model': 'flux1-krea-dev-fp8.safetensors',
        'model_display': 'FLUX.1-Krea',
        'desc': 'High quality model from Black Forest Labs, can work with FLUX tools to achieve high-quality output',
        'status': 1,
        'order': 7,
        'workflow_file': {
            "gen": 'flux_krea_nunchaku.json',
            "hires_fix": 'flux_dev_upscale.json',
        },
        'img2img_workflow_info': {},
        # 扩图默认配置
        'enlarge_img_config': {},
        # 重绘配置
        'local_redraw_config': {},
        'default_config': {
            "seed": -1,
            "steps": 20,
            'width': 1024,
            'height': 1024,
            "cfg": 3.5,
            "samplerName": "euler",
            "scheduler": "simple",
            "denoise": 1.0,
            "positivePrompt": "",
            "negativePrompt": "",
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fflux.1-schnell.webp",
            "modelType": "SDXL"
        }
    },
    # flux art turbo
    'd20550db-3eb2-4f3f-85b1-ef3c523f164e': {
        'obj': GenAdvanceImageWorkflow,
        'type': 'gen_img_advance_art_turbo',
        'model': '',
        'model_display': 'PicLumen Art V1 Turbo',
        'desc': '',
        'status': 1,
        'order': 4,
        'workflow_file': {
            "gen": 'flux_art_turbo.json',
        },
        'img2img_workflow_info': {
        },
        'default_config': {
            "seed": -1,
            "steps": 6,
            'width': 1024,
            'height': 1024,
            "cfg": 5.0,
            "samplerName": "euler",
            "scheduler": "karras",
            "denoise": 1.0,
            "positivePrompt": "",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2FPicLumen%20Anime%20V2.png",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },
    # flux dev turbo
    '35a50220-dc15-4437-9b5c-fae658d3d67d': {
        'obj': GenAdvanceImageWorkflow,
        'type': 'gen_img_advance_flux_dev_turbo',
        'model': '',
        'model_display': 'FLUX.1-dev-turbo',
        'desc': '',
        'status': 1,
        'order': 7,
        'workflow_file': {
            "gen": 'flux_dev_turbo.json',
        },
        'img2img_workflow_info': {
        },
        'default_config': {
            "seed": -1,
            "steps": 20,
            'width': 1024,
            'height': 1024,
            "cfg": 5.0,
            "samplerName": "euler",
            "scheduler": "karras",
            "denoise": 1.0,
            "positivePrompt": "",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2Fflux.1-schnell.webp",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },
    # flux schnell turbo
    '37ddad94-b4b8-4bb1-9cd1-afd05ee97002': {
        'obj': GenAdvanceImageWorkflow,
        'type': 'gen_img_advance_flux_schnell_turbo',
        'model': '',
        'model_display': 'FLUX.1-schnell-turbo',
        'desc': '',
        'status': 1,
        'order': 4,
        'workflow_file': {
            "gen": 'flux_schnell_turbo.json',
        },
        'img2img_workflow_info': {
        },
        'default_config': {
            "seed": -1,
            "steps": 4,
            'width': 1024,
            'height': 1024,
            "cfg": 5.0,
            "samplerName": "euler",
            "scheduler": "karras",
            "denoise": 1.0,
            "positivePrompt": "",
            "hiresFixDenoise": 0.5,
            "hiresScale": 2.0,
            "modelAbility": None,
            "modelAvatar": "https://piclumen-1324066212.cos.na-siliconvalley.myqcloud.com/avatarPicture%2FPicLumen%20Lineart%20V1.png",
            "modelType": "SDXL",
            'defaultHdFixDenoise': 0.38
        }
    },

}
