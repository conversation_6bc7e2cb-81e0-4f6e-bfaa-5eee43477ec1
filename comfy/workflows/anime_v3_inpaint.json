{"3": {"inputs": {"seed": 550921586266987, "steps": 25, "cfg": 7, "sampler_name": "euler_ancestral", "scheduler": "simple", "denoise": 1, "model": ["78", 0], "positive": ["15", 0], "negative": ["15", 1], "latent_image": ["70", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"text": "red hat", "clip": ["78", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "7": {"inputs": {"text": "NSFW, watermask", "clip": ["78", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["75", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "11": {"inputs": {"image": "clipspace/clipspace-mask-190874887.79999995.png [input]", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "15": {"inputs": {"strength": 0.5, "start_percent": 0, "end_percent": 1, "positive": ["6", 0], "negative": ["7", 0], "control_net": ["17", 0], "image": ["54", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet (Advanced)"}}, "16": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "17": {"inputs": {"type": "repaint", "control_net": ["16", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "48": {"inputs": {"mask": ["50", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "50": {"inputs": {"mask": ["72", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "54": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["11", 0], "source": ["48", 0], "mask": ["72", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "63": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["11", 0], "source": ["8", 0], "mask": ["64", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "64": {"inputs": {"amount": 4, "mask": ["72", 0]}, "class_type": "MaskBlur+", "_meta": {"title": "🔧 Mask Blur"}}, "65": {"inputs": {"filename_prefix": "ComfyUI", "images": ["63", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "70": {"inputs": {"grow_mask_by": 6, "pixels": ["54", 0], "vae": ["75", 2], "mask": ["72", 0]}, "class_type": "VAEEncodeForInpaint", "_meta": {"title": "VAE Encode (for Inpainting)"}}, "71": {"inputs": {"image": "ComfyUI_13280_.png", "channel": "red", "upload": "image"}, "class_type": "LoadImageMask", "_meta": {"title": "Load Image (as <PERSON>)"}}, "72": {"inputs": {"grow": 4, "blur": 0, "mask": ["71", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}, "75": {"inputs": {"ckpt_name": "animagineXL40_v4Opt.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "76": {"inputs": {"lora_name": "LCMTurboMix_Euler_A_fix.safetensors", "strength_model": 0.3, "strength_clip": 1, "model": ["75", 0], "clip": ["75", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "77": {"inputs": {"lora_name": "ClearHand-V2.safetensors", "strength_model": 0.8, "strength_clip": 1, "model": ["76", 0], "clip": ["76", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "78": {"inputs": {"lora_name": "cfg_scale_boost.safetensors", "strength_model": 0.15, "strength_clip": 1, "model": ["77", 0], "clip": ["77", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}}