{"6": {"inputs": {"text": "", "clip": ["25", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive Prompt"}}, "25": {"inputs": {"ckpt_name": "PicLumen_Schnell_Art_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "42": {"inputs": {"image": "", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "77": {"inputs": {"max_shift": 0.15, "base_shift": 0, "width": 2048, "height": 2048, "model": ["25", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "87": {"inputs": {"upscale_model": ["88", 0], "image": ["42", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "88": {"inputs": {"model_name": "4xRealWebPhoto_v4_dat2.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "89": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": 0.38, "image": ["87", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "90": {"inputs": {"pixels": ["89", 0], "vae": ["25", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "91": {"inputs": {"seed": 756429561996888, "steps": 6, "cfg": 1, "sampler_name": "euler", "scheduler": "beta", "denoise": 0.3, "model": ["77", 0], "positive": ["6", 0], "negative": ["6", 0], "latent_image": ["90", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "92": {"inputs": {"samples": ["91", 0], "vae": ["25", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "101": {"inputs": {"filename_prefix": "flux_art_upscale", "images": ["92", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}