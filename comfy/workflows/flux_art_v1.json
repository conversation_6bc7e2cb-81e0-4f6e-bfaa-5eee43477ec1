{"6": {"inputs": {"text": "", "clip": ["56", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["59", 0], "vae": ["56", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "flux_art_v1", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "27": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "56": {"inputs": {"ckpt_name": "PicLumen_Schnell_Art_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "59": {"inputs": {"seed": 353058079598729, "steps": 6, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["56", 0], "positive": ["6", 0], "negative": ["6", 0], "latent_image": ["27", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}}