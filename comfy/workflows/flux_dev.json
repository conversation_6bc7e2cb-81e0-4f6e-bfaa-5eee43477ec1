{"6": {"inputs": {"text": "", "clip": ["30", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "8": {"inputs": {"samples": ["31", 0], "vae": ["30", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "flux_dev", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "27": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "30": {"inputs": {"ckpt_name": "FluxD_Mix_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "31": {"inputs": {"seed": 832176619154465, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["39", 0], "positive": ["35", 0], "negative": ["41", 0], "latent_image": ["27", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "35": {"inputs": {"guidance": 3.5, "conditioning": ["6", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "38": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.12, "start": 0, "end": 1, "max_consecutive_cache_hits": -1, "model": ["30", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "39": {"inputs": {"is_patcher": true, "object_to_patch": "diffusion_model", "compiler": "torch.compile", "fullgraph": false, "dynamic": false, "mode": "", "options": "", "disable": false, "backend": "inductor", "model": ["38", 0]}, "class_type": "EnhancedCompileModel", "_meta": {"title": "Compile Model+"}}, "40": {"inputs": {"text": "", "clip": ["30", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt, not work)"}}, "41": {"inputs": {"guidance": 3.5, "conditioning": ["40", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance(None)"}}}