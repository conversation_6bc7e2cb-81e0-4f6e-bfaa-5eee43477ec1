{"3": {"inputs": {"seed": 1071287716670016, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["39", 0], "positive": ["38", 0], "negative": ["38", 1], "latent_image": ["38", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "7": {"inputs": {"text": "", "clip": ["34", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["32", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "flux_dev_enlarge", "images": ["72", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "23": {"inputs": {"text": "", "clip": ["34", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "26": {"inputs": {"guidance": 30, "conditioning": ["23", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "31": {"inputs": {"unet_name": "fluxFillFP8_v10.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "32": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "34": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "38": {"inputs": {"noise_mask": false, "positive": ["26", 0], "negative": ["7", 0], "vae": ["32", 0], "pixels": ["50", 0], "mask": ["51", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "39": {"inputs": {"model": ["31", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "44": {"inputs": {"left": 0, "top": 0, "right": 0, "bottom": 0, "feathering": 0, "image": ["45", 0]}, "class_type": "ImagePadForOutpaint", "_meta": {"title": "Pad Image for Outpainting"}}, "45": {"inputs": {"image": "", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "48": {"inputs": {"image": ["44", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "50": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": ["48", 0], "height": ["48", 1], "crop": "disabled", "image": ["44", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "51": {"inputs": {"width": ["48", 0], "height": ["48", 1], "keep_proportions": false, "mask": ["68", 0]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "68": {"inputs": {"threshold": 20, "mask": ["44", 1]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "72": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": ["73", 0], "height": ["73", 1], "crop": "disabled", "image": ["8", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "73": {"inputs": {"image": ["44", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}}