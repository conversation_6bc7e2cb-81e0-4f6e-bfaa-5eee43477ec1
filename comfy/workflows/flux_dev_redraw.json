{"3": {"inputs": {"seed": 19719277657811, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["39", 0], "positive": ["38", 0], "negative": ["38", 1], "latent_image": ["38", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "7": {"inputs": {"text": "", "clip": ["34", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["32", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "17": {"inputs": {"image": "", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "23": {"inputs": {"text": "", "clip": ["34", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "26": {"inputs": {"guidance": 30, "conditioning": ["23", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "31": {"inputs": {"unet_name": "fluxFillFP8_v10.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "32": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "34": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "38": {"inputs": {"noise_mask": false, "positive": ["26", 0], "negative": ["7", 0], "vae": ["32", 0], "pixels": ["49", 0], "mask": ["54", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "39": {"inputs": {"model": ["31", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "48": {"inputs": {"image": ["17", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "49": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": ["48", 0], "height": ["48", 1], "crop": "disabled", "image": ["17", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "50": {"inputs": {"image": ["17", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "51": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": ["50", 0], "height": ["50", 1], "crop": "disabled", "image": ["8", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "54": {"inputs": {"expand": 0, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 4, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["62", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "62": {"inputs": {"image": "", "channel": "red", "upload": "image"}, "class_type": "LoadImageMask", "_meta": {"title": "Load Image (as <PERSON>)"}}, "63": {"inputs": {"filename_prefix": "ComfyUI", "images": ["51", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}