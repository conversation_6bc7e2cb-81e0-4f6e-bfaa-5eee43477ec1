{"1": {"inputs": {"ckpt_name": "FluxD_Mix_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "3": {"inputs": {"text": "a man", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "4": {"inputs": {"text": "", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"guidance": 3.5, "conditioning": ["3", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "10": {"inputs": {"upscale_model": ["11", 0], "image": ["18", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "11": {"inputs": {"model_name": "4x-UltraSharp.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "12": {"inputs": {"pixels": ["14", 0], "vae": ["1", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "14": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": 0.38, "image": ["10", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "15": {"inputs": {"seed": 534439213485355, "steps": 12, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 0.5, "model": ["20", 0], "positive": ["7", 0], "negative": ["4", 0], "latent_image": ["12", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "16": {"inputs": {"samples": ["15", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "17": {"inputs": {"filename_prefix": "flux_dev_upscale", "images": ["16", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "18": {"inputs": {"image": "test (7).jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "19": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.12, "start": 0, "end": 1, "max_consecutive_cache_hits": -1, "model": ["1", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "20": {"inputs": {"is_patcher": true, "object_to_patch": "diffusion_model", "compiler": "torch.compile", "fullgraph": false, "dynamic": false, "mode": "", "options": "", "disable": false, "backend": "inductor", "model": ["19", 0]}, "class_type": "EnhancedCompileModel", "_meta": {"title": "Compile Model+"}}}