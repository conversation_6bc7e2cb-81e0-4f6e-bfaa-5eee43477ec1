{"23": {"inputs": {"text": "", "clip": ["233", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negative - Leave Empty"}}, "51": {"inputs": {"guidance": 3.5, "conditioning": ["229", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "59": {"inputs": {"images": ["84", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "83": {"inputs": {"seed": 520630305246457, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["237", 0], "positive": ["51", 0], "negative": ["23", 0], "latent_image": ["235", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "84": {"inputs": {"samples": ["83", 0], "vae": ["234", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "229": {"inputs": {"text": "woman making cake", "clip": ["233", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "232": {"inputs": {"unet_name": "flux1-krea-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "233": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "234": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "235": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "236": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.12, "start": 0, "end": 1, "max_consecutive_cache_hits": -1, "model": ["232", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "237": {"inputs": {"is_patcher": true, "object_to_patch": "diffusion_model", "compiler": "torch.compile", "fullgraph": false, "dynamic": false, "mode": "", "options": "", "disable": false, "backend": "inductor", "model": ["236", 0]}, "class_type": "EnhancedCompileModel", "_meta": {"title": "Compile Model+"}}}