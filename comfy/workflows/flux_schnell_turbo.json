{"5": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空潜空间图像"}}, "6": {"inputs": {"text": "anime girl holding a big card show text \"We are under maintenance. Please try again later\"", "clip": ["37", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码（提示）"}}, "8": {"inputs": {"samples": ["13", 0], "vae": ["10", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "9": {"inputs": {"filename_prefix": "flux_schnell_turbo", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "10": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "13": {"inputs": {"noise": ["25", 0], "guider": ["22", 0], "sampler": ["16", 0], "sigmas": ["17", 0], "latent_image": ["5", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "自定义采样器（高级）"}}, "16": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "17": {"inputs": {"scheduler": "simple", "steps": 4, "denoise": 1, "model": ["30", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "基本调度器"}}, "22": {"inputs": {"model": ["30", 0], "conditioning": ["6", 0]}, "class_type": "BasicGuider", "_meta": {"title": "基本引导器"}}, "25": {"inputs": {"noise_seed": 605153047005184}, "class_type": "RandomNoise", "_meta": {"title": "随机噪声"}}, "30": {"inputs": {"model_path": "svdq-int4-flux.1-s<PERSON>nell", "cache_threshold": 0, "attention": "nunchaku-fp16", "cpu_offload": "auto", "device_id": 0, "data_type": "bfloat16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FLUX DiT Loader"}}, "37": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}}