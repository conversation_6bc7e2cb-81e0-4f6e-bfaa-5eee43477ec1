{"6": {"inputs": {"text": "", "clip": ["25", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive Prompt"}}, "11": {"inputs": {"seed": 576489623018137, "steps": 3, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 0.5, "model": ["25", 0], "positive": ["6", 0], "negative": ["6", 0], "latent_image": ["20", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "12": {"inputs": {"filename_prefix": "flux_upscale", "images": ["13", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "13": {"inputs": {"samples": ["11", 0], "vae": ["25", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "20": {"inputs": {"pixels": ["43", 0], "vae": ["25", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "22": {"inputs": {"upscale_model": ["23", 0], "image": ["42", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "23": {"inputs": {"model_name": "4x-UltraSharp.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "25": {"inputs": {"ckpt_name": "PicLumen_Schnell_Art_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "42": {"inputs": {"image": "origin.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "43": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": 0.38, "image": ["22", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}}