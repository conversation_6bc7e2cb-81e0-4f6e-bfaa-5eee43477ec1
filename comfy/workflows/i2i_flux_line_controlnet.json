{"23": {"inputs": {"ckpt_name": "FluxD_Mix_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "24": {"inputs": {"text": "a woman with pink shirt", "clip": ["23", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "25": {"inputs": {"text": "", "clip": ["23", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "26": {"inputs": {"seed": 284759131566702, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["40", 0], "positive": ["36", 0], "negative": ["36", 1], "latent_image": ["36", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "27": {"inputs": {"guidance": 30, "conditioning": ["24", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "30": {"inputs": {"image": "image (2).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "31": {"inputs": {"samples": ["26", 0], "vae": ["23", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "32": {"inputs": {"filename_prefix": "flux_dev_content", "images": ["31", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "36": {"inputs": {"positive": ["27", 0], "negative": ["25", 0], "vae": ["23", 2], "pixels": ["39", 0]}, "class_type": "InstructPixToPixConditioning", "_meta": {"title": "InstructPixToPixConditioning"}}, "37": {"inputs": {"image": ["41", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "39": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": ["37", 0], "height": ["37", 1], "crop": "disabled", "image": ["41", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "40": {"inputs": {"lora_name": "flux1-canny-dev-lora.safetensors", "strength_model": 0.8, "model": ["23", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "41": {"inputs": {"low_threshold": 100, "high_threshold": 200, "resolution": 512, "image": ["30", 0]}, "class_type": "CannyEdgePreprocessor", "_meta": {"title": "<PERSON><PERSON>"}}}