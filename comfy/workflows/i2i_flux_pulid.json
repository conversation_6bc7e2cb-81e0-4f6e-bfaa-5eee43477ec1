{"70": {"inputs": {"ckpt_name": "FluxD_Mix_v1.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "71": {"inputs": {"text": "a high-quality professional portrait of a man, close up, black suit, gray background, upper body, looking at viewer, Super - Resolution, Megapixel, f/2.8 aperture, confident and composed expression, standing against a neutral, softly lit background. realistic, capturing a professional and elegant appearance.", "clip": ["70", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "72": {"inputs": {"text": "", "clip": ["70", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "73": {"inputs": {"seed": 1078021001441985, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["87", 0], "positive": ["75", 0], "negative": ["72", 0], "latent_image": ["86", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "75": {"inputs": {"guidance": 3.5, "conditioning": ["71", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "78": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "80": {"inputs": {"provider": "CUDA"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "83": {"inputs": {"image": "Elon_Musk_Royal_Society_(cropped).jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "84": {"inputs": {"samples": ["73", 0], "vae": ["70", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "85": {"inputs": {"filename_prefix": "flux_pulid", "images": ["84", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "86": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "87": {"inputs": {"weight": 0.8, "start_at": 0, "end_at": 1, "fusion": "mean", "fusion_weight_max": 1, "fusion_weight_min": 0, "train_step": 1000, "use_gray": true, "model": ["70", 0], "pulid_flux": ["88", 0], "eva_clip": ["78", 0], "face_analysis": ["80", 0], "image": ["83", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "88": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}}