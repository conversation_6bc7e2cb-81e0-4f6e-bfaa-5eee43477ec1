{"3": {"inputs": {"seed": 13123, "steps": 30, "cfg": 4.5, "sampler_name": "dpm_2", "scheduler": "normal", "denoise": 1, "model": ["14", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": "1 cat", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "NSFW,watermake", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "piclumen_styleRefer", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "12": {"inputs": {"image": "Stylized-Drawing.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "14": {"inputs": {"weight": 0.95, "weight_type": "style transfer", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["4", 0], "ipadapter": ["15", 0], "image": ["17", 0], "clip_vision": ["16", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "15": {"inputs": {"ipadapter_file": "ip-adapter-plus_sdxl_vit-h.bin"}, "class_type": "IPAdapterModelLoader", "_meta": {"title": "IPAdapter Model Loader"}}, "16": {"inputs": {"clip_name": "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "17": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0.15, "image": ["12", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}}