{"3": {"inputs": {"seed": 729223437052698, "steps": 25, "cfg": 4.5, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["4", 0], "positive": ["15", 0], "negative": ["15", 1], "latent_image": ["76", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Load Checkpoint"}}, "6": {"inputs": {"text": "Korean", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "7": {"inputs": {"text": "NSFW, monochrome", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "piclumen_contentRefer", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "15": {"inputs": {"strength": 0.5, "start_percent": 0, "end_percent": 1, "positive": ["6", 0], "negative": ["7", 0], "control_net": ["17", 0], "image": ["74", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet (Advanced)"}}, "16": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "17": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["16", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "67": {"inputs": {"image": "_DSC1033_min.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "74": {"inputs": {"merge_with_lineart": "lineart_standard", "resolution": 1280, "lineart_lower_bound": 0, "lineart_upper_bound": 1, "object_min_size": 36, "object_connectivity": 1, "image": ["67", 0]}, "class_type": "AnyLineArtPreprocessor_aux", "_meta": {"title": "AnyLine Lineart"}}, "76": {"inputs": {"width": ["80", 0], "height": ["80", 1], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "80": {"inputs": {"image": ["67", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}}