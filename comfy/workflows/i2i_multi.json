{"1": {"inputs": {"image": "upload_c16c9230672fa064e7916344ce068fdb.webp", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "2": {"inputs": {"merge_with_lineart": "lineart_standard", "resolution": 1280, "lineart_lower_bound": 0, "lineart_upper_bound": 1, "object_min_size": 36, "object_connectivity": 1, "image": ["1", 0]}, "class_type": "AnyLineArtPreprocessor_aux", "_meta": {"title": "AnyLine Lineart"}}, "6": {"inputs": {"strength": 0.5, "start_percent": 0, "end_percent": 1, "positive": ["25", 0], "negative": ["21", 0], "control_net": ["8", 0], "image": ["2", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "7": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "8": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["7", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "9": {"inputs": {"seed": 756349011308122, "steps": 25, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 1, "model": ["24", 0], "positive": ["6", 0], "negative": ["6", 1], "latent_image": ["28", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "10": {"inputs": {"pulid_file": "ip-adapter_pulid_sdxl_fp16.safetensors"}, "class_type": "PulidMode<PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load PuLID Model"}}, "11": {"inputs": {"ipadapter_file": "ip-adapter-plus_sdxl_vit-h.bin"}, "class_type": "IPAdapterModelLoader", "_meta": {"title": "IPAdapter Model Loader"}}, "12": {"inputs": {"clip_name": "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "13": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0.15, "image": ["26", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "14": {"inputs": {"provider": "CUDA"}, "class_type": "PulidInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID)"}}, "15": {"inputs": {}, "class_type": "PulidEvaClipLoader", "_meta": {"title": "<PERSON><PERSON> (PuLID)"}}, "18": {"inputs": {"samples": ["9", 0], "vae": ["27", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "19": {"inputs": {"image": "upload_c16c9230672fa064e7916344ce068fdb.webp", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "21": {"inputs": {"text": "NSFW,watermark", "clip": ["27", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "23": {"inputs": {"weight": 0.85, "weight_type": "style transfer", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["27", 0], "ipadapter": ["11", 0], "image": ["13", 0], "clip_vision": ["12", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "24": {"inputs": {"method": "style", "weight": 0.7, "start_at": 0, "end_at": 1, "model": ["23", 0], "pulid": ["10", 0], "eva_clip": ["15", 0], "face_analysis": ["14", 0], "image": ["19", 0]}, "class_type": "A<PERSON>ly<PERSON><PERSON><PERSON>", "_meta": {"title": "Apply PuLID"}}, "25": {"inputs": {"text": "", "clip": ["27", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "26": {"inputs": {"image": "upload_c16c9230672fa064e7916344ce068fdb.webp", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "27": {"inputs": {"ckpt_name": "PicLumen_HXL_Anime_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "28": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "29": {"inputs": {"filename_prefix": "aiease_i2i_multi", "images": ["18", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}