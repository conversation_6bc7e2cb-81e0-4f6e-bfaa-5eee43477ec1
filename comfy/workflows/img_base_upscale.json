{"4": {"inputs": {"ckpt_name": "PicLumen_HXL_Anime_v2.safetensors", "key_opt": "", "vae_speedup": "disable", "mode": "Auto"}, "class_type": "PicLumenLoader", "_meta": {"title": "Load Checkpoint - PicLumen"}}, "6": {"inputs": {"text": "1girl", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "text, watermark", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["25", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "10": {"inputs": {"model_name": "4x-UltraSharp.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "15": {"inputs": {"upscale_model": ["10", 0], "image": ["8", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "16": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": 0.375, "image": ["15", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "19": {"inputs": {"pixels": ["16", 0], "vae": ["4", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "20": {"inputs": {"seed": 545511984269466, "steps": 12, "cfg": 8, "sampler_name": "euler_ancestral", "scheduler": "normal", "denoise": 0.38, "model": ["4", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["19", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "21": {"inputs": {"samples": ["20", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "23": {"inputs": {"filename_prefix": "aiease_base_upscale", "images": ["21", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "24": {"inputs": {"image": "xx.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "25": {"inputs": {"pixels": ["24", 0], "vae": ["4", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}}