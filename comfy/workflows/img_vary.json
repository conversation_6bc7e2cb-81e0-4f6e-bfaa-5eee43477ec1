{"3": {"inputs": {"seed": 1063075286072281, "steps": 25, "cfg": 4.5, "sampler_name": "euler", "scheduler": "karras", "denoise": 0.9, "model": ["27", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["48", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"text": "", "clip": ["27", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "", "clip": ["27", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["27", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "aiease_vary", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "27": {"inputs": {"ckpt_name": "PicLumen_HXL_Anime_v2.safetensors", "key_opt": "", "vae_speedup": "disable", "mode": "Auto"}, "class_type": "PicLumenLoader", "_meta": {"title": "Load Checkpoint - PicLumen"}}, "47": {"inputs": {"pixels": ["52", 0], "vae": ["27", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "48": {"inputs": {"amount": 1, "samples": ["47", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "52": {"inputs": {"image": "", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}}