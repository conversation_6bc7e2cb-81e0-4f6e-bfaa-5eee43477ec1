{"3": {"inputs": {"seed": 789940707168832, "steps": 25, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 0.7, "model": ["53", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["48", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"text": ["55", 0], "clip": ["53", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "NSFW, watermark", "clip": ["53", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["53", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "aiease_vary_wd", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "47": {"inputs": {"pixels": ["56", 0], "vae": ["53", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "48": {"inputs": {"amount": 1, "samples": ["47", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "52": {"inputs": {"image": "input.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "53": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": "", "vae_speedup": "disable", "mode": "Auto"}, "class_type": "PicLumenLoader", "_meta": {"title": "Load Checkpoint - PicLumen"}}, "55": {"inputs": {"model": "wd-v1-4-moat-tagger-v2", "threshold": 0.35, "character_threshold": 0.85, "replace_underscore": false, "trailing_comma": false, "exclude_tags": "", "image": ["56", 0]}, "class_type": "WD14Tagger|pysssss", "_meta": {"title": "WD14 Tagger 🐍"}}, "56": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 1, "image": ["52", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "ImageScaleToTotalPixels"}}}