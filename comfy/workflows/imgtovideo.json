{"6": {"inputs": {"text": "A woman dancing\n", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "Overexposure, static, blurred details, subtitles, paintings, pictures, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, mutilated, redundant fingers, poorly painted hands, poorly painted faces, deformed, disfigured, deformed limbs, fused fingers, cluttered background, three legs, a lot of people in the background, upside down", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["66", 1], "vae": ["39", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "38": {"inputs": {"clip_name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "type": "wan", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "加载CLIP"}}, "39": {"inputs": {"vae_name": "wan_2.1_vae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "49": {"inputs": {"clip_name": "clip_vision_h.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "加载CLIP视觉"}}, "50": {"inputs": {"width": ["55", 3], "height": ["55", 4], "length": ["78", 0], "batch_size": 1, "positive": ["6", 0], "negative": ["7", 0], "vae": ["39", 0], "clip_vision_output": ["51", 0], "start_image": ["55", 0]}, "class_type": "WanImageToVideo", "_meta": {"title": "Wan图像到视频"}}, "51": {"inputs": {"crop": "none", "clip_vision": ["49", 0], "image": ["55", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP视觉编码"}}, "52": {"inputs": {"image": "Snipaste_2025-06-03_20-10-38.jpg"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "55": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 720, "background_color": "#000000", "image": ["52", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "图层工具：按宽高比缩放 V2"}}, "65": {"inputs": {"cfg": 1, "model": ["77", 0], "positive": ["50", 0], "negative": ["50", 1]}, "class_type": "CFGGuider", "_meta": {"title": "CFG引导器"}}, "66": {"inputs": {"noise": ["67", 0], "guider": ["65", 0], "sampler": ["68", 0], "sigmas": ["71", 0], "latent_image": ["50", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "自定义采样器（高级）"}}, "67": {"inputs": {"noise_seed": 821636469068214}, "class_type": "RandomNoise", "_meta": {"title": "随机噪波"}}, "68": {"inputs": {"sampler_name": "uni_pc"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "71": {"inputs": {"scheduler": "simple", "steps": 10, "denoise": 1, "model": ["77", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "基本调度器"}}, "76": {"inputs": {"unet_name": "Wan14Bi2vFusioniX_fp16.safetensors", "weight_dtype": "default"}, "class_type": "EnhancedLoadDiffusionModel", "_meta": {"title": "Load Diffusion Model+"}}, "77": {"inputs": {"shift": 2.0000000000000004, "model": ["76", 0]}, "class_type": "ModelSamplingSD3", "_meta": {"title": "采样算法（SD3）"}}, "78": {"inputs": {"value": 49}, "class_type": "PrimitiveInt", "_meta": {"title": "帧数"}}, "80": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "imgtovideo", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "images": ["8", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}}