{"3": {"inputs": {"seed": 729223437052698, "steps": 25, "cfg": 7, "sampler_name": "euler", "scheduler": "karras", "denoise": 1, "model": ["81", 0], "positive": ["15", 0], "negative": ["15", 1], "latent_image": ["76", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"text": "", "clip": ["81", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "7": {"inputs": {"text": "simple-toned,(monochrome:1.6),(colorless:1.7),(nsfw), (worst quality:1.5), (low quality:1.5), (normal quality:1.5), lowres, watermark, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, username, blurry, artist name", "clip": ["81", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["81", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "aiease_line_recolor", "images": ["82", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "15": {"inputs": {"strength": 0.6, "start_percent": 0, "end_percent": 1, "positive": ["6", 0], "negative": ["7", 0], "control_net": ["17", 0], "image": ["67", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet (Advanced)"}}, "16": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "17": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["16", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "67": {"inputs": {"image": "", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "76": {"inputs": {"width": ["80", 0], "height": ["80", 1], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "80": {"inputs": {"image": ["67", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "81": {"inputs": {"ckpt_name": "PicLumen_HXL_Anime_v2.safetensors", "key_opt": "", "mode": "Auto"}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Shared Checkpoint Loader (Inspire)"}}, "82": {"inputs": {"width": 1024, "height": 1024, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "width_input": ["83", 0], "height_input": ["83", 1], "image": ["8", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "83": {"inputs": {"image": ["67", 0]}, "class_type": "ImageGenResolutionFromImage", "_meta": {"title": "Generation Resolution From Image"}}}