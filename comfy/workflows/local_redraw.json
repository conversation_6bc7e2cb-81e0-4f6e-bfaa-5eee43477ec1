{"3": {"inputs": {"seed": 147226203815635, "steps": 25, "cfg": 4.5, "sampler_name": "dpm_2", "scheduler": "karras", "denoise": 1, "model": ["4", 0], "positive": ["15", 0], "negative": ["15", 1], "latent_image": ["70", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors", "key_opt": ""}, "class_type": "CheckpointLoaderSimpleShared //Inspire", "_meta": {"title": "Load Checkpoint"}}, "6": {"inputs": {"text": "red t-shirts", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "7": {"inputs": {"text": "NSFW, watermask", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "11": {"inputs": {"image": "e30f89bca5af32e388ab7d2b5fa167c5.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "15": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["6", 0], "negative": ["7", 0], "control_net": ["17", 0], "image": ["54", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet (Advanced)"}}, "16": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "17": {"inputs": {"type": "repaint", "control_net": ["16", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "48": {"inputs": {"mask": ["50", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "50": {"inputs": {"mask": ["72", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "54": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["11", 0], "source": ["48", 0], "mask": ["72", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "63": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["11", 0], "source": ["8", 0], "mask": ["64", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "64": {"inputs": {"amount": 4, "mask": ["72", 0]}, "class_type": "MaskBlur+", "_meta": {"title": "🔧 Mask Blur"}}, "65": {"inputs": {"filename_prefix": "aiease_local_redraw", "images": ["63", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "70": {"inputs": {"grow_mask_by": 6, "pixels": ["54", 0], "vae": ["4", 2], "mask": ["72", 0]}, "class_type": "VAEEncodeForInpaint", "_meta": {"title": "VAE Encode (for Inpainting)"}}, "71": {"inputs": {"image": "c4139d0e81d14aefc3b454fb52c60422.png", "channel": "red", "upload": "image"}, "class_type": "LoadImageMask", "_meta": {"title": "Load Image (as <PERSON>)"}}, "72": {"inputs": {"grow": 4, "blur": 0, "mask": ["71", 0]}, "class_type": "INPAINT_ExpandMask", "_meta": {"title": "Expand Mask"}}}