{"2": {"inputs": {"noise": ["16", 0], "guider": ["6", 0], "sampler": ["4", 0], "sigmas": ["5", 0], "latent_image": ["8", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "自定义采样器（高级）"}}, "3": {"inputs": {"samples": ["2", 1], "vae": ["11", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "4": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "5": {"inputs": {"scheduler": "beta", "steps": 10, "denoise": 1, "model": ["13", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "基本调度器"}}, "6": {"inputs": {"cfg": 1, "model": ["42", 0], "positive": ["8", 0], "negative": ["8", 1]}, "class_type": "CFGGuider", "_meta": {"title": "CFG引导器"}}, "7": {"inputs": {"text": "Overexposure, static, blurred details, subtitles, paintings, pictures, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, mutilated, redundant fingers, poorly painted hands, poorly painted faces, deformed, disfigured, deformed limbs, fused fingers, cluttered background, three legs, a lot of people in the background, upside down", "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"width": ["44", 0], "height": ["45", 0], "length": ["46", 0], "batch_size": 1, "positive": ["21", 0], "negative": ["7", 0], "vae": ["11", 0]}, "class_type": "WanImageToVideo", "_meta": {"title": "Wan图像到视频"}}, "9": {"inputs": {"clip_name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "type": "wan", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "加载CLIP"}}, "11": {"inputs": {"vae_name": "wan_2.1_vae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "13": {"inputs": {"shift": 2.0000000000000004, "model": ["47", 0]}, "class_type": "ModelSamplingSD3", "_meta": {"title": "采样算法（SD3）"}}, "16": {"inputs": {"noise_seed": 614190273829733}, "class_type": "RandomNoise", "_meta": {"title": "随机噪波"}}, "21": {"inputs": {"text": "high quality nature video featuring a red panda balancing on a bamboo stem while a bird lands on it's head, on the background there is a waterfall", "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "42": {"inputs": {"rel_l1_thresh": 0.30000000000000004, "start_percent": 1, "end_percent": 1, "cache_device": "offload_device", "coefficients": "i2v_480", "model": ["13", 0]}, "class_type": "WanVideoTeaCacheKJ", "_meta": {"title": "WanVideo <PERSON> (native)"}}, "43": {"inputs": {"value": 3}, "class_type": "easy int", "_meta": {"title": "时长"}}, "44": {"inputs": {"value": 832}, "class_type": "easy int", "_meta": {"title": "宽"}}, "45": {"inputs": {"value": 480}, "class_type": "easy int", "_meta": {"title": "高"}}, "46": {"inputs": {"value": "a*16+1", "a": ["43", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "47": {"inputs": {"unet_name": "Wan2.1_T2V_14B_FusionX-Q4_K_S.gguf"}, "class_type": "UnetLoaderGGUF", "_meta": {"title": "Unet Loader (GGUF)"}}, "48": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "texttovideo", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "images": ["3", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}}