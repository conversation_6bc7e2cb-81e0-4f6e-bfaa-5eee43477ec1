{"45": {"inputs": {"image": "piclumen-1754372124755.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "79": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "91": {"inputs": {"tile_size": 768, "overlap": 64, "samples": ["109", 0], "vae": ["104", 0]}, "class_type": "VAEDecodeTiled", "_meta": {"title": "VAE Decode (Tiled)"}}, "99": {"inputs": {"tile_width": ["108", 0], "tile_height": ["108", 1], "image": ["140", 0]}, "class_type": "TTP_Image_Tile_Batch", "_meta": {"title": "TTP_Image_Tile_Batch"}}, "100": {"inputs": {"text": "Low quality", "clip": ["79", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "102": {"inputs": {"text": "6K,High definition, high quality", "clip": ["79", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "104": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "108": {"inputs": {"width_factor": 2, "height_factor": 3, "overlap_rate": 0.2, "image": ["140", 0]}, "class_type": "TTP_Tile_image_size", "_meta": {"title": "TTP_Tile_image_size"}}, "109": {"inputs": {"seed": 458886311187784, "steps": 12, "cfg": 1, "sampler_name": "euler", "scheduler": "exponential", "denoise": 0.9, "model": ["146", 0], "positive": ["102", 0], "negative": ["100", 0], "latent_image": ["110", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "110": {"inputs": {"pixels": ["113", 0], "vae": ["104", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "113": {"inputs": {"image": ["99", 0]}, "class_type": "ImpactImageBatchToImageList", "_meta": {"title": "Image Batch to Image List"}}, "117": {"inputs": {"images": ["91", 0]}, "class_type": "ImageListToImageBatch", "_meta": {"title": "Image List to Image Batch"}}, "118": {"inputs": {"padding": 64, "tiles": ["117", 0], "positions": ["99", 1], "original_size": ["99", 2], "grid_size": ["99", 3]}, "class_type": "TTP_Image_Assy", "_meta": {"title": "TTP_Image_Assy"}}, "125": {"inputs": {"images": ["118", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "138": {"inputs": {"model_name": "4x-UltraSharp.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "139": {"inputs": {"upscale_model": ["138", 0], "image": ["45", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "140": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": 0.5, "image": ["139", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "145": {"inputs": {"model_path": "svdq-int4_r32-flux.1-dev.safetensors", "cache_threshold": 0, "attention": "nunchaku-fp16", "cpu_offload": "auto", "device_id": 0, "data_type": "bfloat16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FLUX DiT Loader"}}, "146": {"inputs": {"lora_name": "FLUX.1-Turbo-Alpha.safetensors", "lora_strength": 1, "model": ["145", 0]}, "class_type": "NunchakuFluxLoraLoader", "_meta": {"title": "Nunchaku FLUX.1 LoRA Loader"}}}