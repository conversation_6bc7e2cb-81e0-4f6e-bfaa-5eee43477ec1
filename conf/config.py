import os

from dotenv import load_dotenv, find_dotenv
from pydantic import BaseConfig
from typing import List


class Config(BaseConfig):
    # 加载环境变量
    load_dotenv(find_dotenv(), override=True)
    APP_DEBUG: bool = True
    VERSION: str = "0.0.1"
    APP_TITLE: str = "easeapi"

    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    CONF_DIR: str = os.path.join(BASE_DIR, "conf")
    LOG_DIR: str = os.path.join(BASE_DIR, "logs")
    # 跨域请求
    CORS_ORIGINS: List = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List = ["*"]
    CORS_ALLOW_HEADERS: List = ["*"]


settings = Config()
