import os
import yaml

from conf.config import settings

TMP_UPLOAD_FILEPATH = ''  # 临时上传文件目录
STATIC_FILEPATH = ''  # 静态文件目录

DEFAULT_CLIENT_ID = "piclumen-api"  # comfy的client id

CALLBACK_APIKEY = "db73f5b9-7d99-43c6-b07c-96ebe36916e1"  # 回调使用的apikey

yaml_setting_path = os.path.join(settings.CONF_DIR, 'settings.yaml')

if not os.path.exists(yaml_setting_path):
    raise FileNotFoundError("settings.yaml not exists")

with open(yaml_setting_path, 'r', encoding='utf-8') as f:
    cfg = f.read()
    ys = yaml.load(cfg, Loader=yaml.Loader)

locals().update(ys)
