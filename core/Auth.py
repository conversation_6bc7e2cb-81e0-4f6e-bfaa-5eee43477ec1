from fastapi import Request, HTTPException
from starlette.status import HTTP_403_FORBIDDEN

apikey_list = [
    "acfc0073-155a-4d30-aab6-8b718f8f5108"
]


async def check_auth(req: Request):
    """
    """
    apikey = req.headers.get('authorization', "") or req.headers.get("Authorization", "")
    if not apikey:
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN, detail="no apikey provided"
        )
    apikey = apikey.split('Bearer')[-1].strip()
    if apikey not in apikey_list:
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN, detail=f"no apiuser for apikey {apikey}"
        )
    return apikey
