from typing import Union
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from fastapi import HTTPException
from starlette.requests import Request
from starlette.responses import J<PERSON>NResponse


async def http_error_handler(_: Request, exc: HTTPException) -> JSONResponse:
    return JSONResponse(
        status_code=exc.status_code,
        content={
            'code': exc.status_code,
            'result': None,
            'message': exc.detail
        }
    )


async def http422_error_handler(
        _: Request, exc: Union[RequestValidationError, ValidationError]
) -> JSONResponse:
    return JSONResponse(
        status_code=200,
        content={
            'code': 400,
            'result': None,
            'message': exc.errors()[0].get('msg') if len(exc.errors()) > 0 else exc.errors()
        }
    )


async def common_error_handler(_: Request, exc: Exception) -> JSONResponse:
    return J<PERSON>NResponse(
        content={
            'code': 500,
            'result': None,
            'message': 'server error, please try again later'
        }
    )
