import logging
from logging.handlers import TimedRotatingFileHandler
import os

from conf.config import settings


def get_logger(name: str) -> logging.Logger:
    # 确保日志目录存在
    log_dir = settings.LOG_DIR
    log_path = os.path.join(log_dir, name)
    if not os.path.exists(log_path):
        os.makedirs(log_path)

    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # 创建处理器，每天生成一个新的日志文件，保留30天的日志
    handler = TimedRotatingFileHandler(
        filename=os.path.join(log_dir, name, f"{name}.log"),
        when="midnight",
        interval=1,
        backupCount=30,
        encoding="utf-8"
    )
    handler.setLevel(logging.INFO)

    # 创建一个handler，用于输出到控制台
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)  # 设置控制台handler的日志级别

    # 创建日志格式
    formatter = logging.Formatter(
        '%(pathname)s %(funcName)s (%(lineno)d) %(thread)d - %(asctime)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)

    # 将处理器添加到日志记录器
    logger.addHandler(handler)
    logger.addHandler(console_handler)
    return logger


error_logger = get_logger(name="error_log")
