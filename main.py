from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.exceptions import RequestValidationError

from conf.config import settings
from core import Events, Router
from core.Exception import http_error_handler, http422_error_handler

application = FastAPI(
    debug=settings.APP_DEBUG,
    title=settings.APP_TITLE,
    version=settings.VERSION,
    description="aiease gen image api"
)

application.add_exception_handler(HTTPException, http_error_handler)
application.add_exception_handler(RequestValidationError, http422_error_handler)

# 事件监听
application.add_event_handler("startup", Events.startup(application))
application.add_event_handler("shutdown", Events.stopping(application))

# 路由
application.include_router(Router.router)

app = application
