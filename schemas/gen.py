import logging

from pydantic import BaseModel, field_validator, model_validator

from comfy.defines import model_info_dict, rmbg_workflow_map, upscale_workflow_map, image_control_pre_process_map
from utils.consts import *
from utils.utils import get_sensitive_word, get_seed

logger = logging.getLogger(__name__)


# 定义 Pydantic 模型
class GenImageItem(BaseModel):
    prompt: str
    model_id: str
    seed: int
    negative_prompt: str
    sampler: str
    scheduler: str
    steps: int
    cfg: float
    denoise: float
    batch_size: int = DEFAULT_BATCH_SIZE
    height: int
    width: int
    speed: str = 'normal'
    priority: int | None = None
    extra_data: dict | None = {}
    callback_url: str = ""
    mark_id: str = ""
    gen_mode: str = "quality"
    img2img_info: dict | None = None
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    accelerated: bool = True  # 是否模型加速

    class Config:
        protected_namespaces = ()

    @field_validator("prompt")
    def check_prompt(cls, attr: str) -> str:
        # 去除敏感词
        sensitive_words = get_sensitive_word(attr)
        if sensitive_words:
            for word in sensitive_words:
                attr = attr.replace(word, "")
        return attr

    @model_validator(mode='after')
    def check_img2img_info(cls, values):
        """
        检查图生图 并且设置默认值
        :param values:
        :return:
        """
        img2img_info = values.img2img_info
        img2img_info = img2img_info or {}
        if not img2img_info:
            values.img2img_info = {}
            return values
        model_id = values.model_id
        style_list = img2img_info.get('style_list', [])
        if 1 < len(style_list) <= 3:
            style = 'multiRefer'
        elif len(style_list) == 1:
            style = style_list[0].get('style')
            weight = style_list[0].get('weight')
            img_url = style_list[0].get('img_url', "")
            img2img_info['img_url'] = img_url
            if weight is not None:
                try:
                    weight = float(weight)
                except ValueError:
                    raise ValueError(f"weight must be a float")
                if weight < 0 or weight > 1:
                    raise ValueError(f"weight must be between 0 and 1")
                img2img_info['weight'] = weight
        else:
            raise ValueError(f"style must be in {style_list}")
        default_conf = model_info_dict.get(model_id, {}).get('img2img_workflow_info', {}).get(style, {}).get(
            'default_conf', {})
        if not default_conf:
            raise ValueError(f"not support img2img")
        img2img_info['style'] = style
        sampler_name = default_conf.get('sampler_name')
        scheduler = default_conf.get('scheduler')
        cfg = default_conf.get('cfg')
        steps = default_conf.get('steps')
        if sampler_name:
            values.sampler = sampler_name
        if scheduler:
            values.scheduler = scheduler
        if cfg:
            values.cfg = cfg
        if steps:
            values.steps = steps
        return values

    @field_validator("gen_mode")
    def check_gen_mode(cls, attr: str) -> str:
        if attr not in ["quality", "fast"]:
            raise ValueError("gen_mode must be either 'quality' or 'fast'")
        return attr

    @field_validator("model_id")
    def check_model_id(cls, attr: str) -> str:
        model_info = model_info_dict.get(attr, {})
        if not model_info or not model_info.get("status"):
            raise ValueError(f"'{attr}' is not a valid model id")
        return attr

    @field_validator("sampler")
    def check_sampler(cls, attr: str) -> str:
        if attr not in SAMPLER_LIST:
            raise ValueError(f"'{attr}' is not a valid sampler name")
        return attr

    @field_validator("scheduler")
    def check_scheduler(cls, attr: str) -> str:
        if attr not in SCHEDULER_LIST:
            raise ValueError(f"'{attr}' is not a valid scheduler name")
        return attr

    @field_validator("steps")
    def check_steps(cls, attr: int) -> int:
        if any([
            attr > 60,
            attr < 1,
        ]):
            raise ValueError(f"'{attr}' is not a valid number of steps, 1 <= steps <= 60")
        return int(attr)

    @field_validator("cfg")
    def check_cfg(cls, attr: float) -> float:
        if any([
            attr > 30,
            attr < 1,
        ]):
            raise ValueError(f"'{attr}' is not a valid cfg value, 1 <= cfg <= 30")
        return float(attr)

    @field_validator("denoise")
    def check_denoise(cls, attr: float) -> float:
        if any([
            attr > 1,
            attr < 0,
        ]):
            raise ValueError(f"'{attr}' is not a valid denoise value, 0 <= denoise <= 1")
        return float(attr)

    @field_validator("batch_size")
    def check_batch_size(cls, attr: int) -> int:
        if any([
            attr > 8,
            attr < 0,
        ]):
            raise ValueError(f"'{attr}' is not a valid batch size, 0 < batch_size < 8")
        return int(attr)

    @field_validator("height")
    def check_height(cls, attr: int) -> int:
        if any([
            attr > 2526,
            attr < 0,
        ]):
            raise ValueError(f"'{attr}' is not a valid height value, 0 < height < 2526")
        return int(attr)

    @field_validator("width")
    def check_width(cls, attr: int) -> int:
        if any([
            attr > 2526,
            attr < 0,
        ]):
            raise ValueError(f"'{attr}' is not a valid width value, 0 < height < 2526")
        return int(attr)

    @field_validator("seed")
    def check_seed(cls, attr: int) -> int:
        if attr is None or attr == -1:
            attr = get_seed()
        return attr

    @field_validator("speed")
    def check_speed(cls, attr: str) -> str:
        if attr not in SPEED_DICT:
            raise ValueError(f"'{attr}' is not a valid speed value, choices are {list(SPEED_DICT.keys())}")
        return attr

    @field_validator("priority")
    def check_priority(cls, attr: int | None) -> int | None:
        if attr == -1:
            return None
        if attr is None:
            return attr
        if any([
            attr > 10,
            attr < 1,
        ]):
            raise ValueError(f"'{attr}' is not a valid priority, 0 < priority < 10")
        return attr

    @field_validator("callback_url")
    def check_callback_url(cls, attr: str) -> str:
        return attr

    @field_validator("extra_data")
    def check_extra_data(cls, attr: {}) -> {}:
        attr = attr or {}
        anime_style = attr.get('anime_style')
        if anime_style is not None:
            if any([
                anime_style > 1,
                anime_style < 0,
            ]):
                raise ValueError('Anime style must be between 0 and 1')

        # 高清修复配置
        hd_fix = attr.get('hires_fix', {})
        if hd_fix:
            upscale_method_list = ['nearest-exact', 'bilinear', 'area', 'bicubic', 'lanczos']
            switch = hd_fix.get('switch', False) or hd_fix.get('status', False)  # 高清修复开关
            upscale_method = hd_fix.get('upscale_method', 'lanczos')  # 方法 默认lanczos
            hd_fix_denoise = hd_fix.get('denoise')  # 降噪(重绘幅度)
            scale = hd_fix.get('scale', 1.5)

            if upscale_method not in upscale_method_list:
                raise ValueError('Upscale method must be one of the following: {}'.format(upscale_method_list))
            if hd_fix_denoise is not None:
                if hd_fix_denoise > 1 or hd_fix_denoise < 0:
                    raise ValueError('Upscale denoise must be between 0 and 1')
            if scale > 4 or scale < 0:
                raise ValueError('Scale must be between 0 and 1')
            hd_fix['switch'] = bool(switch)
        return attr


class RembgItem(BaseModel):
    type: str
    img_url: str  # 图片地址
    callback_url: str = ""  # 回调地址
    mark_id: str = ""  # 随机id
    prompt_id: str = ""
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    accelerated: bool = True  # 是否模型加速

    @field_validator("type")
    def check_type(cls, attr: str) -> str:
        if attr not in rmbg_workflow_map:
            raise ValueError(f"'{attr}' is not a valid type, choices are {list(rmbg_workflow_map.keys())}")
        return attr

class ImageControlPreProcessItem(BaseModel):
    type: str
    img_url: str  # 图片地址
    callback_url: str = ""  # 回调地址
    mark_id: str = ""  # 随机id
    prompt_id: str = ""
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    accelerated: bool = True  # 是否模型加速

    @field_validator("type")
    def check_type(cls, attr: str) -> str:
        if attr not in image_control_pre_process_map:
            raise ValueError(f"'{attr}' is not a valid type, choices are {list(image_control_pre_process_map.keys())}")
        return attr

class UpscaleItem(BaseModel):
    type: str = "upscale_normal"
    scale_by: float
    img_url: str  # 图片地址
    callback_url: str = ""
    mark_id: str = ""
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    accelerated: bool = True  # 是否模型加速

    @field_validator("type")
    def check_type(cls, attr: str) -> str:
        if attr not in upscale_workflow_map:
            raise ValueError(f"'{attr}' is not a valid type, choices are {list(upscale_workflow_map.keys())}")
        return attr


class HiresFixItem(BaseModel):
    img_url: str  # 图片地址
    callback_url: str = ""  # 回调地址
    mark_id: str = ""
    upscale_method: str = "lanczos"
    prompt_id: str = ""
    scale_by: float = 1.5
    hd_denoise: float = 0.38
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    accelerated: bool = True  # 是否模型加速
    model_id: str = ""
    prompt: str = ""
    seed: int | None = None
    negative_prompt: str = ""
    sampler: str = ""
    scheduler: str = ""
    steps: int | None = None
    cfg: float | None = None
    denoise: float | None = None
    height: int | None = None
    width: int | None = None

    @field_validator("upscale_method")
    def check_upscale_method(cls, attr: str) -> str:
        upscale_method_list = ['nearest-exact', 'bilinear', 'area', 'bicubic', 'lanczos']
        if attr not in upscale_method_list:
            raise ValueError("Upscale method must be one of the following: {}".format(upscale_method_list))
        return attr

    @field_validator("scale_by")
    def check_upscale_scale_by(cls, attr: float) -> float:
        if not all([
            attr >= 1,
            attr <= 4
        ]):
            raise ValueError("Upscale scale must be between 1 and 5")
        return attr

    @field_validator("hd_denoise")
    def check_hd_denoise(cls, attr: float) -> float:
        if attr > 1 or attr < 0:
            raise ValueError("Upscale denoise must be between 0 and 1")
        return attr


class LocalRedrawItem(BaseModel):
    img_url: str  # 图片地址
    model_id: str  # 图片地址
    mask_img_url: str  # 图片地址
    callback_url: str = ""  # 回调地址
    mark_id: str = ""  # 随机id
    prompt: str = ""  # 提示词
    denoise: float | None = 0.9  # 降噪(重绘幅度)
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    accelerated: bool = True  # 是否模型加速

    @field_validator("denoise")
    def check_denoise(cls, attr: float) -> float:
        if attr is None:
            attr = 0.9
        if attr > 1 or attr < 0:
            raise ValueError("denoise must be between 0 and 1")
        return attr

    @field_validator("model_id")
    def check_model_id(cls, attr: str) -> str:
        model_info = model_info_dict.get(attr, {})
        if not model_info or not model_info.get("status"):
            raise ValueError(f"'{attr}' is not a valid model id")
        return attr


class EnlargeImageItem(BaseModel):
    model_id: str  # 模型
    img_url: str  # 图片地址
    callback_url: str = ""  # 回调地址
    mark_id: str = ""  # 随机id
    prompt: str = ""  # 正向提示词
    batch_size: int = 1  # 生图张数 当前仅支持一张
    denoise: float | None = 1  # 降噪(重绘幅度)
    left: int = 200  # 左边
    top: int = 0  # 顶部
    right: int = 200  # 右边
    bottom: int = 200  # 底部
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    accelerated: bool = True  # 是否模型加速

    @field_validator("denoise")
    def check_denoise(cls, attr: float) -> float:
        if attr is None:
            attr = 1
        if attr > 1 or attr < 0:
            raise ValueError("denoise must be between 0 and 1")
        return attr

    @field_validator("model_id")
    def check_model_id(cls, attr: str) -> str:
        model_info = model_info_dict.get(attr, {})
        if not model_info or not model_info.get("status"):
            raise ValueError(f"'{attr}' is not a valid model id")
        enlarge_img_config = model_info.get('enlarge_img_config')
        if not enlarge_img_config:
            raise ValueError(f"{attr} not supported enlarge image config")
        return attr

    @field_validator("batch_size")
    def check_batch_size(cls, attr: int) -> int:
        if any([
            attr > 4,
            attr < 0,
        ]):
            raise ValueError(f"'{attr}' is not a valid batch size, 0 < batch_size < 4")
        return int(attr)


class LineRecolorItem(BaseModel):
    img_url: str  # 图片地址
    model_id: str  # 模型id
    callback_url: str = ""  # 回调地址
    mark_id: str = ""  # 随机id
    prompt: str = ""  # 正向提示词
    batch_size: int = 1  # 生图张数 当前仅支持一张
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    accelerated: bool = True  # 是否模型加速

    @field_validator("batch_size")
    def check_batch_size(cls, attr: int) -> int:
        if any([
            attr > 4,
            attr < 0,
        ]):
            raise ValueError(f"'{attr}' is not a valid batch size, 0 < batch_size < 4")
        return int(attr)

    @field_validator("model_id")
    def check_model_id(cls, attr: str) -> str:
        model_info = model_info_dict.get(attr, {})
        if not model_info or not model_info.get("status"):
            raise ValueError(f"'{attr}' is not a valid model id")
        mode_type = model_info.get('type')
        if mode_type != 'gen_img_normal':
            raise ValueError(f"'{attr}' is not a valid model id")
        return attr


class ImgVaryItem(BaseModel):
    img_url: str  # 图片地址
    model_id: str = ""  # 模型id
    callback_url: str = ""  # 回调地址
    mark_id: str = ""  # 随机id
    prompt: str | None = ""  # 正向提示词
    negative_prompt: str | None = ""  # 负向提示词
    batch_size: int = 1  # 生图张数 当前仅支持一张
    strength: str  # 程度
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    flag: str | None = ""  # 标识
    accelerated: bool = True  # 是否模型加速

    @field_validator("batch_size")
    def check_batch_size(cls, attr: int) -> int:
        if any([
            attr > 4,
            attr < 0,
        ]):
            raise ValueError(f"'{attr}' is not a valid batch size, 0 < batch_size < 4")
        return int(attr)

    @field_validator("strength")
    def check_strength(cls, attr: str) -> float:
        if attr not in vary_denoise_map:
            raise ValueError(f"'{attr}' is not a valid strength")
        return vary_denoise_map[attr]

    @field_validator("model_id")
    def check_model_id(cls, attr: str) -> str:
        model_info = model_info_dict.get(attr, {})
        if not model_info or not model_info.get("status"):
            raise ValueError(f"'{attr}' is not a valid model id")
        img_vary_config = model_info.get('img_vary_config', {})
        if not img_vary_config:
            raise ValueError(f"{attr} not supported img_vary_config")
        return attr


class Image2TextItem(BaseModel):
    """
    提示词反推
    """
    img_url: str  # 图片地址
    type: str = "desc"
    callback_url: str = ""  # 回调地址
    mark_id: str = ""  # 随机id
    prompt_id: str = ""
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址

    @field_validator("type")
    def check_type(cls, attr: str) -> str:
        type_dict = {
            'tag': 'Booru tag list',
            'desc': 'Descriptive'
        }
        if attr not in type_dict:
            raise ValueError(f"'{attr}' is not a valid type")
        return type_dict[attr]


class TextToVideoItem(BaseModel):
    """
    文生视频参数模型
    """
    prompt: str  # 正向提示词
    negative_prompt: str = ""  # 反向提示词
    model_id: str  # 视频模型id，必填
    seed: int  # 随机种子
    width: int = 832  # 视频宽度
    height: int = 480  # 视频高度
    duration: int = 3  # 视频时长(秒)
    steps: int = 10  # 采样步数
    cfg: float = 1.0  # CFG引导强度
    sampler: str = "euler"  # 采样器
    scheduler: str = "beta"  # 调度器
    callback_url: str = ""  # 回调地址
    mark_id: str = ""  # 随机id
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    accelerated: bool = True  # 是否模型加速

    @field_validator("duration")
    def check_duration(cls, attr: int) -> int:
        if attr < 1 or attr > 10:
            raise ValueError("duration must be between 1 and 10 seconds")
        return attr

    @field_validator("width", "height")
    def check_dimensions(cls, attr: int) -> int:
        if attr < 256 or attr > 1920:
            raise ValueError("width and height must be between 256 and 1920")
        return attr

    @field_validator("model_id")
    def check_model_id(cls, attr: str) -> str:
        if not attr:
            raise ValueError("model_id is required for video generation")
        # 这里可以添加更多的模型ID验证逻辑
        return attr


class ImageToVideoItem(BaseModel):
    """
    图生视频参数模型
    """
    img_url: str  # 输入图片地址
    prompt: str  # 正向提示词
    negative_prompt: str = ""  # 反向提示词
    model_id: str  # 视频模型id，必填
    seed: int  # 随机种子
    frames: int = 49  # 视频帧数
    steps: int = 10  # 采样步数
    cfg: float = 1.0  # CFG引导强度
    sampler: str = "uni_pc"  # 采样器
    scheduler: str = "simple"  # 调度器
    callback_url: str = ""  # 回调地址
    mark_id: str = ""  # 随机id
    address_id: str | None = ""  # 算力地址id
    address: str | None = ""  # 算力地址
    accelerated: bool = True  # 是否模型加速

    @field_validator("frames")
    def check_frames(cls, attr: int) -> int:
        if attr < 16 or attr > 120:
            raise ValueError("frames must be between 16 and 120")
        return attr

    @field_validator("model_id")
    def check_model_id(cls, attr: str) -> str:
        if not attr:
            raise ValueError("model_id is required for video generation")
        # 这里可以添加更多的模型ID验证逻辑
        return attr
