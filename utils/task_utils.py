from comfy.defines import model_info_dict, workflow_map

from comfy.apis import BaseComfy


def get_workflow_obj(task: dict) -> [BaseComfy | None]:
    """
    根据任务类型处理工作流
    """
    comfy_server = task.get('comfy_server', '')
    params = task.get('params', {})
    task_type = task.get('task_type', '')
    prompt_id = task.get('prompt_id', '')
    client_id = task.get('client_id', '')
    model_id = params.get('model_id')
    img2img_info = params.get('img2img_info', {})
    if model_id:
        # 生图
        workflow_info = model_info_dict.get(model_id, {})
    else:
        workflow_info = workflow_map.get(task_type, {})
    if not workflow_info:
        return None
    obj_class = workflow_info['obj']
    model = workflow_info['model']
    if model_id:
        gen_type = params.get('gen_type', 'gen')  # 可以是生图 也会有高清修复生图
        workflow_file = workflow_info.get('workflow_file', {}).get(gen_type)
    else:
        workflow_file = workflow_info['workflow_file']

    if img2img_info:
        obj_class = workflow_info.get('img2img_obj')
        if not obj_class:
            # 该模型不支持图生图
            return None
        # 图生图模式
        img2img_workflow_info = workflow_info.get('img2img_workflow_info', {})
        style = img2img_info.get('style')
        if style not in img2img_workflow_info:
            return None
        workflow_file = img2img_workflow_info.get(style, {}).get('file')
    if not workflow_file:
        return None
    kwargs = {
        'extra_params': params,
        'model_name': model,
        'workflow_file': workflow_file,
        'priority': params.get('priority'),  # 优先级, 用于插队
    }
    obj = obj_class(prompt_id=prompt_id, client_id=client_id, server_address=comfy_server, **kwargs)
    return obj
